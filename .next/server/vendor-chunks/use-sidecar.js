"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-sidecar";
exports.ids = ["vendor-chunks/use-sidecar"];
exports.modules = {

/***/ "(ssr)/./node_modules/use-sidecar/dist/es5/config.js":
/*!*****************************************************!*\
  !*** ./node_modules/use-sidecar/dist/es5/config.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.setConfig = exports.config = void 0;\nexports.config = {\n    onError: function (e) { return console.error(e); },\n};\nvar setConfig = function (conf) {\n    Object.assign(exports.config, conf);\n};\nexports.setConfig = setConfig;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLXNpZGVjYXIvZGlzdC9lczUvY29uZmlnLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGlCQUFpQixHQUFHLGNBQWM7QUFDbEMsY0FBYztBQUNkLDRCQUE0QiwwQkFBMEI7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jdmV0cy12ZXRlcmluYXJ5LW5leHRqcy8uL25vZGVfbW9kdWxlcy91c2Utc2lkZWNhci9kaXN0L2VzNS9jb25maWcuanM/NmFjOSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuc2V0Q29uZmlnID0gZXhwb3J0cy5jb25maWcgPSB2b2lkIDA7XG5leHBvcnRzLmNvbmZpZyA9IHtcbiAgICBvbkVycm9yOiBmdW5jdGlvbiAoZSkgeyByZXR1cm4gY29uc29sZS5lcnJvcihlKTsgfSxcbn07XG52YXIgc2V0Q29uZmlnID0gZnVuY3Rpb24gKGNvbmYpIHtcbiAgICBPYmplY3QuYXNzaWduKGV4cG9ydHMuY29uZmlnLCBjb25mKTtcbn07XG5leHBvcnRzLnNldENvbmZpZyA9IHNldENvbmZpZztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sidecar/dist/es5/config.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-sidecar/dist/es5/env.js":
/*!**************************************************!*\
  !*** ./node_modules/use-sidecar/dist/es5/env.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.env = void 0;\nvar detect_node_es_1 = __webpack_require__(/*! detect-node-es */ \"(ssr)/./node_modules/detect-node-es/es5/node.js\");\nexports.env = {\n    isNode: detect_node_es_1.isNode,\n    forceCache: false,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLXNpZGVjYXIvZGlzdC9lczUvZW52LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELFdBQVc7QUFDWCx1QkFBdUIsbUJBQU8sQ0FBQyx1RUFBZ0I7QUFDL0MsV0FBVztBQUNYO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2N2ZXRzLXZldGVyaW5hcnktbmV4dGpzLy4vbm9kZV9tb2R1bGVzL3VzZS1zaWRlY2FyL2Rpc3QvZXM1L2Vudi5qcz8wMzMxIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5lbnYgPSB2b2lkIDA7XG52YXIgZGV0ZWN0X25vZGVfZXNfMSA9IHJlcXVpcmUoXCJkZXRlY3Qtbm9kZS1lc1wiKTtcbmV4cG9ydHMuZW52ID0ge1xuICAgIGlzTm9kZTogZGV0ZWN0X25vZGVfZXNfMS5pc05vZGUsXG4gICAgZm9yY2VDYWNoZTogZmFsc2UsXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sidecar/dist/es5/env.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-sidecar/dist/es5/exports.js":
/*!******************************************************!*\
  !*** ./node_modules/use-sidecar/dist/es5/exports.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.exportSidecar = void 0;\nvar tslib_1 = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\nvar React = tslib_1.__importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar SideCar = function (_a) {\n    var sideCar = _a.sideCar, rest = tslib_1.__rest(_a, [\"sideCar\"]);\n    if (!sideCar) {\n        throw new Error('Sidecar: please provide `sideCar` property to import the right car');\n    }\n    var Target = sideCar.read();\n    if (!Target) {\n        throw new Error('Sidecar medium not found');\n    }\n    return React.createElement(Target, tslib_1.__assign({}, rest));\n};\nSideCar.isSideCarExport = true;\nfunction exportSidecar(medium, exported) {\n    medium.useMedium(exported);\n    return SideCar;\n}\nexports.exportSidecar = exportSidecar;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLXNpZGVjYXIvZGlzdC9lczUvZXhwb3J0cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxxQkFBcUI7QUFDckIsY0FBYyxtQkFBTyxDQUFDLHVEQUFPO0FBQzdCLGlDQUFpQyxtQkFBTyxDQUFDLHdHQUFPO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBEQUEwRDtBQUMxRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jdmV0cy12ZXRlcmluYXJ5LW5leHRqcy8uL25vZGVfbW9kdWxlcy91c2Utc2lkZWNhci9kaXN0L2VzNS9leHBvcnRzLmpzPzFmYjIiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmV4cG9ydFNpZGVjYXIgPSB2b2lkIDA7XG52YXIgdHNsaWJfMSA9IHJlcXVpcmUoXCJ0c2xpYlwiKTtcbnZhciBSZWFjdCA9IHRzbGliXzEuX19pbXBvcnRTdGFyKHJlcXVpcmUoXCJyZWFjdFwiKSk7XG52YXIgU2lkZUNhciA9IGZ1bmN0aW9uIChfYSkge1xuICAgIHZhciBzaWRlQ2FyID0gX2Euc2lkZUNhciwgcmVzdCA9IHRzbGliXzEuX19yZXN0KF9hLCBbXCJzaWRlQ2FyXCJdKTtcbiAgICBpZiAoIXNpZGVDYXIpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdTaWRlY2FyOiBwbGVhc2UgcHJvdmlkZSBgc2lkZUNhcmAgcHJvcGVydHkgdG8gaW1wb3J0IHRoZSByaWdodCBjYXInKTtcbiAgICB9XG4gICAgdmFyIFRhcmdldCA9IHNpZGVDYXIucmVhZCgpO1xuICAgIGlmICghVGFyZ2V0KSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignU2lkZWNhciBtZWRpdW0gbm90IGZvdW5kJyk7XG4gICAgfVxuICAgIHJldHVybiBSZWFjdC5jcmVhdGVFbGVtZW50KFRhcmdldCwgdHNsaWJfMS5fX2Fzc2lnbih7fSwgcmVzdCkpO1xufTtcblNpZGVDYXIuaXNTaWRlQ2FyRXhwb3J0ID0gdHJ1ZTtcbmZ1bmN0aW9uIGV4cG9ydFNpZGVjYXIobWVkaXVtLCBleHBvcnRlZCkge1xuICAgIG1lZGl1bS51c2VNZWRpdW0oZXhwb3J0ZWQpO1xuICAgIHJldHVybiBTaWRlQ2FyO1xufVxuZXhwb3J0cy5leHBvcnRTaWRlY2FyID0gZXhwb3J0U2lkZWNhcjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sidecar/dist/es5/exports.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-sidecar/dist/es5/hoc.js":
/*!**************************************************!*\
  !*** ./node_modules/use-sidecar/dist/es5/hoc.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.sidecar = void 0;\nvar tslib_1 = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\nvar React = tslib_1.__importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar hook_1 = __webpack_require__(/*! ./hook */ \"(ssr)/./node_modules/use-sidecar/dist/es5/hook.js\");\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction sidecar(importer, errorComponent) {\n    var ErrorCase = function () { return errorComponent; };\n    return function Sidecar(props) {\n        var _a = (0, hook_1.useSidecar)(importer, props.sideCar), Car = _a[0], error = _a[1];\n        if (error && errorComponent) {\n            return ErrorCase;\n        }\n        // @ts-expect-error type shenanigans\n        return Car ? React.createElement(Car, tslib_1.__assign({}, props)) : null;\n    };\n}\nexports.sidecar = sidecar;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLXNpZGVjYXIvZGlzdC9lczUvaG9jLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGVBQWU7QUFDZixjQUFjLG1CQUFPLENBQUMsdURBQU87QUFDN0IsaUNBQWlDLG1CQUFPLENBQUMsd0dBQU87QUFDaEQsYUFBYSxtQkFBTyxDQUFDLGlFQUFRO0FBQzdCO0FBQ0E7QUFDQSxrQ0FBa0M7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWlFO0FBQ2pFO0FBQ0E7QUFDQSxlQUFlIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3ZldHMtdmV0ZXJpbmFyeS1uZXh0anMvLi9ub2RlX21vZHVsZXMvdXNlLXNpZGVjYXIvZGlzdC9lczUvaG9jLmpzP2FhYjgiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnNpZGVjYXIgPSB2b2lkIDA7XG52YXIgdHNsaWJfMSA9IHJlcXVpcmUoXCJ0c2xpYlwiKTtcbnZhciBSZWFjdCA9IHRzbGliXzEuX19pbXBvcnRTdGFyKHJlcXVpcmUoXCJyZWFjdFwiKSk7XG52YXIgaG9va18xID0gcmVxdWlyZShcIi4vaG9va1wiKTtcbi8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvYmFuLXR5cGVzXG5mdW5jdGlvbiBzaWRlY2FyKGltcG9ydGVyLCBlcnJvckNvbXBvbmVudCkge1xuICAgIHZhciBFcnJvckNhc2UgPSBmdW5jdGlvbiAoKSB7IHJldHVybiBlcnJvckNvbXBvbmVudDsgfTtcbiAgICByZXR1cm4gZnVuY3Rpb24gU2lkZWNhcihwcm9wcykge1xuICAgICAgICB2YXIgX2EgPSAoMCwgaG9va18xLnVzZVNpZGVjYXIpKGltcG9ydGVyLCBwcm9wcy5zaWRlQ2FyKSwgQ2FyID0gX2FbMF0sIGVycm9yID0gX2FbMV07XG4gICAgICAgIGlmIChlcnJvciAmJiBlcnJvckNvbXBvbmVudCkge1xuICAgICAgICAgICAgcmV0dXJuIEVycm9yQ2FzZTtcbiAgICAgICAgfVxuICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yIHR5cGUgc2hlbmFuaWdhbnNcbiAgICAgICAgcmV0dXJuIENhciA/IFJlYWN0LmNyZWF0ZUVsZW1lbnQoQ2FyLCB0c2xpYl8xLl9fYXNzaWduKHt9LCBwcm9wcykpIDogbnVsbDtcbiAgICB9O1xufVxuZXhwb3J0cy5zaWRlY2FyID0gc2lkZWNhcjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sidecar/dist/es5/hoc.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-sidecar/dist/es5/hook.js":
/*!***************************************************!*\
  !*** ./node_modules/use-sidecar/dist/es5/hook.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useSidecar = void 0;\nvar react_1 = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar env_1 = __webpack_require__(/*! ./env */ \"(ssr)/./node_modules/use-sidecar/dist/es5/env.js\");\nvar cache = new WeakMap();\nvar NO_OPTIONS = {};\nfunction useSidecar(importer, effect) {\n    var options = (effect && effect.options) || NO_OPTIONS;\n    if (env_1.env.isNode && !options.ssr) {\n        return [null, null];\n    }\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    return useRealSidecar(importer, effect);\n}\nexports.useSidecar = useSidecar;\nfunction useRealSidecar(importer, effect) {\n    var options = (effect && effect.options) || NO_OPTIONS;\n    var couldUseCache = env_1.env.forceCache || (env_1.env.isNode && !!options.ssr) || !options.async;\n    var _a = (0, react_1.useState)(couldUseCache ? function () { return cache.get(importer); } : undefined), Car = _a[0], setCar = _a[1];\n    var _b = (0, react_1.useState)(null), error = _b[0], setError = _b[1];\n    (0, react_1.useEffect)(function () {\n        if (!Car) {\n            importer().then(function (car) {\n                var resolved = effect ? effect.read() : car.default || car;\n                if (!resolved) {\n                    console.error('Sidecar error: with importer', importer);\n                    var error_1;\n                    if (effect) {\n                        console.error('Sidecar error: with medium', effect);\n                        error_1 = new Error('Sidecar medium was not found');\n                    }\n                    else {\n                        error_1 = new Error('Sidecar was not found in exports');\n                    }\n                    setError(function () { return error_1; });\n                    throw error_1;\n                }\n                cache.set(importer, resolved);\n                setCar(function () { return resolved; });\n            }, function (e) { return setError(function () { return e; }); });\n        }\n    }, []);\n    return [Car, error];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sidecar/dist/es5/hook.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-sidecar/dist/es5/index.js":
/*!****************************************************!*\
  !*** ./node_modules/use-sidecar/dist/es5/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.exportSidecar = exports.renderCar = exports.createSidecarMedium = exports.createMedium = exports.setConfig = exports.useSidecar = exports.sidecar = void 0;\nvar hoc_1 = __webpack_require__(/*! ./hoc */ \"(ssr)/./node_modules/use-sidecar/dist/es5/hoc.js\");\nObject.defineProperty(exports, \"sidecar\", ({ enumerable: true, get: function () { return hoc_1.sidecar; } }));\nvar hook_1 = __webpack_require__(/*! ./hook */ \"(ssr)/./node_modules/use-sidecar/dist/es5/hook.js\");\nObject.defineProperty(exports, \"useSidecar\", ({ enumerable: true, get: function () { return hook_1.useSidecar; } }));\nvar config_1 = __webpack_require__(/*! ./config */ \"(ssr)/./node_modules/use-sidecar/dist/es5/config.js\");\nObject.defineProperty(exports, \"setConfig\", ({ enumerable: true, get: function () { return config_1.setConfig; } }));\nvar medium_1 = __webpack_require__(/*! ./medium */ \"(ssr)/./node_modules/use-sidecar/dist/es5/medium.js\");\nObject.defineProperty(exports, \"createMedium\", ({ enumerable: true, get: function () { return medium_1.createMedium; } }));\nObject.defineProperty(exports, \"createSidecarMedium\", ({ enumerable: true, get: function () { return medium_1.createSidecarMedium; } }));\nvar renderProp_1 = __webpack_require__(/*! ./renderProp */ \"(ssr)/./node_modules/use-sidecar/dist/es5/renderProp.js\");\nObject.defineProperty(exports, \"renderCar\", ({ enumerable: true, get: function () { return renderProp_1.renderCar; } }));\nvar exports_1 = __webpack_require__(/*! ./exports */ \"(ssr)/./node_modules/use-sidecar/dist/es5/exports.js\");\nObject.defineProperty(exports, \"exportSidecar\", ({ enumerable: true, get: function () { return exports_1.exportSidecar; } }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sidecar/dist/es5/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-sidecar/dist/es5/medium.js":
/*!*****************************************************!*\
  !*** ./node_modules/use-sidecar/dist/es5/medium.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createSidecarMedium = exports.createMedium = void 0;\nvar tslib_1 = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\nfunction ItoI(a) {\n    return a;\n}\nfunction innerCreateMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    var buffer = [];\n    var assigned = false;\n    var medium = {\n        read: function () {\n            if (assigned) {\n                throw new Error('Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.');\n            }\n            if (buffer.length) {\n                return buffer[buffer.length - 1];\n            }\n            return defaults;\n        },\n        useMedium: function (data) {\n            var item = middleware(data, assigned);\n            buffer.push(item);\n            return function () {\n                buffer = buffer.filter(function (x) { return x !== item; });\n            };\n        },\n        assignSyncMedium: function (cb) {\n            assigned = true;\n            while (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n            }\n            buffer = {\n                push: function (x) { return cb(x); },\n                filter: function () { return buffer; },\n            };\n        },\n        assignMedium: function (cb) {\n            assigned = true;\n            var pendingQueue = [];\n            if (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n                pendingQueue = buffer;\n            }\n            var executeQueue = function () {\n                var cbs = pendingQueue;\n                pendingQueue = [];\n                cbs.forEach(cb);\n            };\n            var cycle = function () { return Promise.resolve().then(executeQueue); };\n            cycle();\n            buffer = {\n                push: function (x) {\n                    pendingQueue.push(x);\n                    cycle();\n                },\n                filter: function (filter) {\n                    pendingQueue = pendingQueue.filter(filter);\n                    return buffer;\n                },\n            };\n        },\n    };\n    return medium;\n}\nfunction createMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    return innerCreateMedium(defaults, middleware);\n}\nexports.createMedium = createMedium;\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction createSidecarMedium(options) {\n    if (options === void 0) { options = {}; }\n    var medium = innerCreateMedium(null);\n    medium.options = tslib_1.__assign({ async: true, ssr: false }, options);\n    return medium;\n}\nexports.createSidecarMedium = createSidecarMedium;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sidecar/dist/es5/medium.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-sidecar/dist/es5/renderProp.js":
/*!*********************************************************!*\
  !*** ./node_modules/use-sidecar/dist/es5/renderProp.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.renderCar = void 0;\nvar tslib_1 = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\nvar React = tslib_1.__importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar react_1 = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nfunction renderCar(WrappedComponent, defaults) {\n    function State(_a) {\n        var stateRef = _a.stateRef, props = _a.props;\n        var renderTarget = (0, react_1.useCallback)(function SideTarget() {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            (0, react_1.useLayoutEffect)(function () {\n                stateRef.current(args);\n            });\n            return null;\n        }, []);\n        // @ts-ignore\n        return React.createElement(WrappedComponent, tslib_1.__assign({}, props, { children: renderTarget }));\n    }\n    var Children = React.memo(function (_a) {\n        var stateRef = _a.stateRef, defaultState = _a.defaultState, children = _a.children;\n        var _b = (0, react_1.useState)(defaultState.current), state = _b[0], setState = _b[1];\n        (0, react_1.useEffect)(function () {\n            stateRef.current = setState;\n        }, []);\n        return children.apply(void 0, state);\n    }, function () { return true; });\n    return function Combiner(props) {\n        var defaultState = React.useRef(defaults(props));\n        var ref = React.useRef(function (state) { return (defaultState.current = state); });\n        return (React.createElement(React.Fragment, null,\n            React.createElement(State, { stateRef: ref, props: props }),\n            React.createElement(Children, { stateRef: ref, defaultState: defaultState, children: props.children })));\n    };\n}\nexports.renderCar = renderCar;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sidecar/dist/es5/renderProp.js\n");

/***/ })

};
;