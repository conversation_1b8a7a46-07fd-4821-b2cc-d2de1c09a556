/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/detect-node-es";
exports.ids = ["vendor-chunks/detect-node-es"];
exports.modules = {

/***/ "(ssr)/./node_modules/detect-node-es/es5/node.js":
/*!*************************************************!*\
  !*** ./node_modules/detect-node-es/es5/node.js ***!
  \*************************************************/
/***/ ((module) => {

eval("// Only Node.JS has a process variable that is of [[Class]] process\nmodule.exports.isNode = Object.prototype.toString.call(typeof process !== 'undefined' ? process : 0) === '[object process]';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZGV0ZWN0LW5vZGUtZXMvZXM1L25vZGUuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxxQkFBcUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jdmV0cy12ZXRlcmluYXJ5LW5leHRqcy8uL25vZGVfbW9kdWxlcy9kZXRlY3Qtbm9kZS1lcy9lczUvbm9kZS5qcz9hYjYyIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIE9ubHkgTm9kZS5KUyBoYXMgYSBwcm9jZXNzIHZhcmlhYmxlIHRoYXQgaXMgb2YgW1tDbGFzc11dIHByb2Nlc3Ncbm1vZHVsZS5leHBvcnRzLmlzTm9kZSA9IE9iamVjdC5wcm90b3R5cGUudG9TdHJpbmcuY2FsbCh0eXBlb2YgcHJvY2VzcyAhPT0gJ3VuZGVmaW5lZCcgPyBwcm9jZXNzIDogMCkgPT09ICdbb2JqZWN0IHByb2Nlc3NdJztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/detect-node-es/es5/node.js\n");

/***/ })

};
;