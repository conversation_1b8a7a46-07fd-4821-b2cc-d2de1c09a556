"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Bug,Calendar,Clock,Heart,Home,Phone,Plane,Scissors,Shield,Siren,Sparkles,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Bug,Calendar,Clock,Heart,Home,Phone,Plane,Scissors,Shield,Siren,Sparkles,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Bug,Calendar,Clock,Heart,Home,Phone,Plane,Scissors,Shield,Siren,Sparkles,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scissors.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Bug,Calendar,Clock,Heart,Home,Phone,Plane,Scissors,Shield,Siren,Sparkles,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Bug,Calendar,Clock,Heart,Home,Phone,Plane,Scissors,Shield,Siren,Sparkles,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Bug,Calendar,Clock,Heart,Home,Phone,Plane,Scissors,Shield,Siren,Sparkles,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/siren.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Bug,Calendar,Clock,Heart,Home,Phone,Plane,Scissors,Shield,Siren,Sparkles,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Bug,Calendar,Clock,Heart,Home,Phone,Plane,Scissors,Shield,Siren,Sparkles,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plane.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Bug,Calendar,Clock,Heart,Home,Phone,Plane,Scissors,Shield,Siren,Sparkles,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Bug,Calendar,Clock,Heart,Home,Phone,Plane,Scissors,Shield,Siren,Sparkles,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Bug,Calendar,Clock,Heart,Home,Phone,Plane,Scissors,Shield,Siren,Sparkles,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Bug,Calendar,Clock,Heart,Home,Phone,Plane,Scissors,Shield,Siren,Sparkles,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Bug,Calendar,Clock,Heart,Home,Phone,Plane,Scissors,Shield,Siren,Sparkles,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Bug,Calendar,Clock,Heart,Home,Phone,Plane,Scissors,Shield,Siren,Sparkles,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./components/Footer.tsx\");\n/* harmony import */ var _components_AppointmentProvider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/AppointmentProvider */ \"(app-pages-browser)/./components/AppointmentProvider.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    const { openAppointmentModal } = (0,_components_AppointmentProvider__WEBPACK_IMPORTED_MODULE_7__.useAppointment)();\n    // Handle smooth scrolling when page loads with hash\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const hash = window.location.hash;\n        if (hash) {\n            const elementId = hash.substring(1); // Remove the # symbol\n            setTimeout(()=>{\n                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.smoothScrollToElement)(elementId);\n            }, 100);\n        }\n    }, []);\n    const services = [\n        {\n            title: \"Vaccinations\",\n            description: \"Essential vaccination programs to protect your pet from common diseases and maintain their immunity.\",\n            image: \"https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?auto=format&fit=crop&w=400&h=300\",\n            icon: _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            title: \"Health Check-ups\",\n            description: \"Comprehensive health examinations to ensure your pet's optimal wellbeing and early detection of health issues.\",\n            image: \"https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?auto=format&fit=crop&w=400&h=300\",\n            icon: _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            title: \"Surgical Procedures\",\n            description: \"Professional surgical services including spaying, neutering, and other necessary procedures.\",\n            image: \"https://images.unsplash.com/photo-1582562124811-c09040d0a901?auto=format&fit=crop&w=400&h=300\",\n            icon: _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            title: \"Dental Care\",\n            description: \"Complete dental health services including cleaning, extractions, and oral health maintenance.\",\n            image: \"https://images.unsplash.com/photo-1601758228041-f3b2795255f1?auto=format&fit=crop&w=400&h=300\",\n            icon: _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            title: \"Diagnostic Testing\",\n            description: \"Advanced diagnostic services including blood work, X-rays, and laboratory testing.\",\n            image: \"https://images.unsplash.com/photo-**********-5c350d0d3c56?auto=format&fit=crop&w=400&h=300\",\n            icon: _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            title: \"Emergency Care\",\n            description: \"24/7 emergency veterinary services for urgent medical situations and critical care.\",\n            image: \"https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?auto=format&fit=crop&w=400&h=300\",\n            icon: _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        },\n        {\n            title: \"Preventative Care\",\n            description: \"Comprehensive preventive treatments to keep your pets healthy and prevent future health issues.\",\n            image: \"https://images.unsplash.com/photo-**********-03cce0bbc87b?auto=format&fit=crop&w=400&h=300\",\n            icon: _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            title: \"Parasite Control\",\n            description: \"Comprehensive parasite prevention and treatment programs for fleas, ticks, and worms.\",\n            image: \"https://images.unsplash.com/photo-1583337130417-3346a1be7dee?auto=format&fit=crop&w=400&h=300\",\n            icon: _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        },\n        {\n            title: \"Local and International Travel\",\n            description: \"Health certificates and travel documentation for pets traveling domestically or internationally.\",\n            image: \"https://images.unsplash.com/photo-1436491865332-7a61a109cc05?auto=format&fit=crop&w=400&h=300\",\n            icon: _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n        },\n        {\n            title: \"Wash & Grooming\",\n            description: \"Professional grooming services including bathing, nail trimming, and coat care.\",\n            image: \"https://images.unsplash.com/photo-**********-8cc77767d783?auto=format&fit=crop&w=400&h=300\",\n            icon: _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        }\n    ];\n    const features = [\n        {\n            icon: _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            title: \"Home Visits\",\n            description: \"No stressful car rides or waiting rooms. We come to you.\",\n            gradient: \"from-blue-100 to-blue-200\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            title: \"Flexible Scheduling\",\n            description: \"Book appointments that fit your busy schedule.\",\n            gradient: \"from-pink-100 to-pink-200\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            title: \"24/7 Support\",\n            description: \"Emergency care available around the clock.\",\n            gradient: \"from-gray-100 to-gray-200\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            title: \"Quick Response\",\n            description: \"Fast response times for urgent situations.\",\n            gradient: \"from-blue-100 to-pink-100\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative pt-20 pb-16 px-4 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-blue-400/10 via-pink-400/5 to-gray-100/20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Professional Veterinary Care\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-5xl lg:text-6xl font-bold text-gray-900 leading-tight\",\n                                                    children: [\n                                                        \"Your Pet's Health is Our\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-pink-500\",\n                                                            children: \" Priority\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl text-gray-600 leading-relaxed\",\n                                                    children: \"CVETS Veterinary Services provides comprehensive, compassionate care for your beloved pets. Led by Dr. Cynthia Nolari, we offer mobile veterinary services bringing professional care directly to your home.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: openAppointmentModal,\n                                                    size: \"lg\",\n                                                    className: \"bg-gradient-to-r from-blue-500 to-pink-500 hover:from-blue-600 hover:to-pink-600 text-white px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Book Appointment\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"w-5 h-5 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    asChild: true,\n                                                    variant: \"outline\",\n                                                    size: \"lg\",\n                                                    className: \"border-2 border-gray-200 bg-white/70 backdrop-blur-sm text-gray-700 hover:bg-gray-50 hover:border-gray-300 px-8 py-4 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/services\",\n                                                        children: \"View Services\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-blue-400 to-pink-400 rounded-3xl transform rotate-3 opacity-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-3xl shadow-2xl overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"https://images.unsplash.com/photo-1601758228041-f3b2795255f1?auto=format&fit=crop&w=600&h=400\",\n                                                    alt: \"Veterinarian examining a pet\",\n                                                    className: \"w-full h-96 object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                className: \"py-32 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-blue-50/30 via-white/30 to-pink-50/30\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-20 space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center space-x-2 bg-gradient-to-r from-blue-100 to-pink-100 rounded-2xl px-8 py-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-semibold text-gray-700\",\n                                                children: \"Why Choose Us\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-900 to-gray-900 bg-clip-text text-transparent leading-tight\",\n                                        children: [\n                                            \"Why Choose Mobile\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-blue-600 to-pink-600 bg-clip-text text-transparent\",\n                                                children: \"Veterinary Care?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n                                        children: \"Experience the convenience and comfort of professional veterinary services delivered directly to your home with unprecedented care and attention.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                                children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"p-8 text-center space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-20 h-20 bg-gradient-to-r \".concat(feature.gradient, \" rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                        className: \"w-10 h-10 text-slate-700\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold text-slate-800 group-hover:text-blue-600 transition-colors duration-300\",\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-slate-600 leading-relaxed\",\n                                                    children: feature.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"services\",\n                className: \"py-32 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-gray-100/50 via-blue-50/50 to-pink-50/50\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-20 space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center space-x-2 bg-gradient-to-r from-pink-100 to-blue-100 rounded-2xl px-8 py-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"w-6 h-6 text-pink-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-semibold text-gray-700\",\n                                                children: \"Our Services\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-pink-900 to-gray-900 bg-clip-text text-transparent leading-tight\",\n                                        children: [\n                                            \"Comprehensive Care\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-pink-600 to-blue-600 bg-clip-text text-transparent\",\n                                                children: \"Tailored to Your Pet\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 leading-relaxed\",\n                                        children: \"Professional veterinary services designed with your pet's comfort and health in mind\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\n                                children: services.map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-48 overflow-hidden rounded-t-3xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: service.image,\n                                                        alt: service.title,\n                                                        className: \"w-full h-full object-cover transition-transform duration-700 group-hover:scale-110\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 right-4 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(service.icon, {\n                                                            className: \"w-6 h-6 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                className: \"p-8 space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-slate-800 group-hover:text-blue-600 transition-colors duration-300\",\n                                                        children: service.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-600 leading-relaxed\",\n                                                        children: service.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mt-16\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    asChild: true,\n                                    size: \"lg\",\n                                    className: \"bg-gradient-to-r from-pink-600 to-blue-700 hover:from-pink-700 hover:to-blue-800 text-white px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/services\",\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"View All Services\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-blue-600 via-pink-600 to-blue-700\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl md:text-5xl font-bold text-white leading-tight\",\n                                    children: [\n                                        \"Ready to Schedule Your Pet's\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\",\n                                            children: \"Appointment?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-blue-100 leading-relaxed\",\n                                    children: \"Book now for convenient, stress-free veterinary care that puts your pet's comfort first\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-6 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: openAppointmentModal,\n                                            size: \"lg\",\n                                            className: \"bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Book Online\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"w-5 h-5 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            asChild: true,\n                                            size: \"lg\",\n                                            variant: \"outline\",\n                                            className: \"text-white border-white/50 bg-white/10 backdrop-blur-sm hover:bg-white hover:text-blue-600 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"tel:0718376311\",\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Bug_Calendar_Clock_Heart_Home_Phone_Plane_Scissors_Shield_Siren_Sparkles_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Call 0718376311\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppointmentForm, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n                lineNumber: 340,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"y1UExG1U3R/Aga6nQhJisV5OqeA=\", false, function() {\n    return [\n        _components_AppointmentProvider__WEBPACK_IMPORTED_MODULE_7__.useAppointment\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});