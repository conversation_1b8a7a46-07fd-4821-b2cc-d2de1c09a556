"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/page",{

/***/ "(app-pages-browser)/./app/blog/page.tsx":
/*!***************************!*\
  !*** ./app/blog/page.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BlogPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./components/Footer.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_Calendar_Clock_Heart_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,Calendar,Clock,Heart,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_Calendar_Clock_Heart_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,Calendar,Clock,Heart,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_Calendar_Clock_Heart_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,Calendar,Clock,Heart,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_Calendar_Clock_Heart_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,Calendar,Clock,Heart,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_Calendar_Clock_Heart_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,Calendar,Clock,Heart,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_Calendar_Clock_Heart_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,Calendar,Clock,Heart,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction BlogPage() {\n    _s();\n    const [posts, setPosts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchPosts = async ()=>{\n            try {\n                const response = await fetch(\"/data/blogs.json\");\n                const data = await response.json();\n                setPosts(data);\n            } catch (error) {\n                console.error(\"Error fetching blog posts:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchPosts();\n    }, []);\n    const categories = [\n        \"All\",\n        ...Array.from(new Set(posts.map((post)=>post.category)))\n    ];\n    const filteredPosts = selectedCategory === \"All\" ? posts : posts.filter((post)=>post.category === selectedCategory);\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        });\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"pt-20 pb-16 px-4 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Loading blog posts...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n            lineNumber: 62,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative pt-20 pb-16 px-4 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-blue-400/10 via-pink-400/5 to-gray-100/20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_Calendar_Clock_Heart_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Veterinary Insights & Tips\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-5xl lg:text-6xl font-bold text-gray-900 leading-tight\",\n                                        children: [\n                                            \"Expert Advice for\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-pink-500\",\n                                                children: \" Pet Care\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto\",\n                                        children: \"Stay informed with the latest veterinary insights, pet care tips, and health advice from Nolari.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-8 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center gap-4\",\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: selectedCategory === category ? \"default\" : \"outline\",\n                                onClick: ()=>setSelectedCategory(category),\n                                className: \"rounded-full px-6 py-2 transition-all duration-300 \".concat(selectedCategory === category ? \"bg-gradient-to-r from-blue-600 to-pink-600 text-white\" : \"border-gray-200 text-gray-700 hover:bg-blue-50\"),\n                                children: category\n                            }, category, false, {\n                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            filteredPosts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                children: \"Featured Article\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"group relative overflow-hidden bg-white/80 backdrop-blur-sm border-0 shadow-2xl rounded-3xl hover:shadow-3xl transition-all duration-500\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative h-64 lg:h-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: filteredPosts[0].image,\n                                                alt: filteredPosts[0].title,\n                                                className: \"w-full h-full object-cover transition-transform duration-700 group-hover:scale-105\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-blue-600/20 to-pink-600/20\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-12 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        className: \"bg-blue-100 text-blue-800\",\n                                                        children: filteredPosts[0].category\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 text-gray-500 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_Calendar_Clock_Heart_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                                lineNumber: 148,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: formatDate(filteredPosts[0].date)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 text-gray-500 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_Calendar_Clock_Heart_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: filteredPosts[0].readTime\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-3xl font-bold text-gray-800 group-hover:text-blue-600 transition-colors duration-300\",\n                                                children: filteredPosts[0].title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 leading-relaxed text-lg\",\n                                                children: filteredPosts[0].excerpt\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_Calendar_Clock_Heart_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-5 h-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 font-medium\",\n                                                        children: filteredPosts[0].author\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                href: \"/blog/\".concat(filteredPosts[0].id),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    className: \"bg-gradient-to-r from-blue-600 to-pink-600 hover:from-blue-700 hover:to-pink-700 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Read Full Article\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_Calendar_Clock_Heart_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                                lineNumber: 174,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                children: \"Latest Articles\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: filteredPosts.slice(1).map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"group relative overflow-hidden bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-48 overflow-hidden rounded-t-3xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: post.image,\n                                                    alt: post.title,\n                                                    className: \"w-full h-full object-cover transition-transform duration-700 group-hover:scale-110\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-4 left-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        className: \"bg-blue-100 text-blue-800\",\n                                                        children: post.category\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 text-sm text-gray-500 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_Calendar_Clock_Heart_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                                    lineNumber: 215,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: formatDate(post.date)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                                    lineNumber: 216,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_Calendar_Clock_Heart_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: post.readTime\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-xl font-bold text-gray-800 group-hover:text-blue-600 transition-colors duration-300 line-clamp-2\",\n                                                    children: post.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm leading-relaxed line-clamp-3\",\n                                                    children: post.excerpt\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_Calendar_Clock_Heart_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: post.author\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2\",\n                                                    children: post.tags.slice(0, 3).map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: \"secondary\",\n                                                            className: \"text-xs bg-gray-100 text-gray-600\",\n                                                            children: tag\n                                                        }, index, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                    href: \"/blog/\".concat(post.id),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        className: \"w-full bg-gradient-to-r from-blue-600 to-pink-600 hover:from-blue-700 hover:to-pink-700 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Read More\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_Calendar_Clock_Heart_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                                    lineNumber: 250,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, post.id, true, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-blue-600 via-pink-600 to-blue-700\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl md:text-5xl font-bold text-white leading-tight\",\n                                    children: [\n                                        \"Stay Updated with\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\",\n                                            children: \"Pet Care Tips\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-blue-100 leading-relaxed\",\n                                    children: \"Get the latest veterinary insights and pet care advice delivered straight to your WhatsApp\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>{\n                                        const message = \"Hi Nolari! I'd like to stay updated with your latest pet care tips and veterinary insights. Please add me to your updates list.\";\n                                        const whatsappUrl = \"https://wa.me/254718376311?text=\".concat(encodeURIComponent(message));\n                                        window.open(whatsappUrl, \"_blank\");\n                                    },\n                                    className: \"bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 text-lg font-semibold\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_Calendar_Clock_Heart_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Subscribe for Updates\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                lineNumber: 262,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogPage, \"C/Nh3X+3CtheU1PipBPuqAGS90E=\");\n_c = BlogPage;\nvar _c;\n$RefreshReg$(_c, \"BlogPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/blog/page.tsx\n"));

/***/ })

});