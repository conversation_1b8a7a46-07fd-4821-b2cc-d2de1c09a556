"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/services/page",{

/***/ "(app-pages-browser)/./app/services/page.tsx":
/*!*******************************!*\
  !*** ./app/services/page.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ServicesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./components/Footer.tsx\");\n/* harmony import */ var _components_AppointmentProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/AppointmentProvider */ \"(app-pages-browser)/./components/AppointmentProvider.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Heart_Scissors_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Heart,Scissors,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Heart_Scissors_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Heart,Scissors,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scissors.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Heart_Scissors_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Heart,Scissors,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Heart_Scissors_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Heart,Scissors,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Heart_Scissors_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Heart,Scissors,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ServicesPage() {\n    _s();\n    const { openAppointmentModal } = (0,_components_AppointmentProvider__WEBPACK_IMPORTED_MODULE_6__.useAppointment)();\n    // Handle smooth scrolling when page loads with hash\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const hash = window.location.hash;\n        if (hash) {\n            const elementId = hash.substring(1); // Remove the # symbol\n            setTimeout(()=>{\n                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.smoothScrollToElement)(elementId);\n            }, 100);\n        }\n    }, []);\n    const services = [\n        {\n            category: \"Medical Care\",\n            gradient: \"from-blue-50 to-cyan-50\",\n            icon: _barrel_optimize_names_CheckCircle_Clock_Heart_Scissors_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            items: [\n                {\n                    name: \"Health Check-ups\",\n                    description: \"Comprehensive physical examinations to assess your pet's overall health and detect early signs of illness.\",\n                    duration: \"45-60 minutes\",\n                    features: [\n                        \"Physical examination\",\n                        \"Weight and vital signs\",\n                        \"Health assessment\",\n                        \"Preventive care recommendations\"\n                    ]\n                },\n                {\n                    name: \"Diagnostic Testing\",\n                    description: \"Advanced diagnostic services including blood work, urinalysis, and other laboratory tests.\",\n                    duration: \"30-45 minutes\",\n                    features: [\n                        \"Blood chemistry panels\",\n                        \"Complete blood count\",\n                        \"Urinalysis\",\n                        \"Parasite screening\"\n                    ]\n                },\n                {\n                    name: \"Emergency Care\",\n                    description: \"Immediate medical attention for urgent health situations and critical care needs.\",\n                    duration: \"Variable\",\n                    features: [\n                        \"24/7 availability\",\n                        \"Urgent care\",\n                        \"Stabilization\",\n                        \"Emergency treatment\"\n                    ]\n                }\n            ]\n        },\n        {\n            category: \"Surgical Services\",\n            gradient: \"from-green-50 to-emerald-50\",\n            icon: _barrel_optimize_names_CheckCircle_Clock_Heart_Scissors_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            items: [\n                {\n                    name: \"Spaying & Neutering\",\n                    description: \"Professional sterilization procedures to prevent unwanted pregnancies and health issues.\",\n                    duration: \"1-2 hours\",\n                    features: [\n                        \"Pre-surgical examination\",\n                        \"Anesthesia monitoring\",\n                        \"Post-operative care\",\n                        \"Pain management\"\n                    ]\n                },\n                {\n                    name: \"Soft Tissue Surgery\",\n                    description: \"Various surgical procedures for treating injuries, masses, and other conditions.\",\n                    duration: \"1-3 hours\",\n                    features: [\n                        \"Tumor removal\",\n                        \"Wound repair\",\n                        \"Abscess treatment\",\n                        \"Foreign body removal\"\n                    ]\n                }\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative pt-20 pb-16 px-4 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-blue-600/10 via-cyan-500/5 to-slate-100/20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Heart_Scissors_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Professional Veterinary Services\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-5xl lg:text-6xl font-bold text-slate-900 leading-tight\",\n                                            children: [\n                                                \"Comprehensive Care for\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600\",\n                                                    children: \" Your Pet\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-slate-600 leading-relaxed max-w-3xl mx-auto\",\n                                            children: \"CVETS offers a full range of veterinary services delivered with compassion and expertise. From routine check-ups to emergency care, we're here for your pet's every need.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-6 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: openAppointmentModal,\n                                            size: \"lg\",\n                                            className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Book Appointment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Heart_Scissors_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-5 h-5 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            asChild: true,\n                                            variant: \"outline\",\n                                            size: \"lg\",\n                                            className: \"border-2 border-slate-200 bg-white/70 backdrop-blur-sm text-slate-700 hover:bg-slate-50 hover:border-slate-300 px-8 py-4 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"tel:0718376311\",\n                                                className: \"flex items-center space-x-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Emergency: 0718376311\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"services-list\",\n                className: \"py-32 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-slate-100/50 via-blue-50/50 to-green-50/50\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-20\",\n                            children: services.map((category, categoryIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-flex items-center space-x-3 bg-gradient-to-r \".concat(category.gradient, \" rounded-2xl px-8 py-4\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(category.icon, {\n                                                        className: \"w-8 h-8 text-slate-700\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-slate-800\",\n                                                        children: category.category\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                            children: category.items.map((service, serviceIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                    className: \"group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                            className: \"pb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                                    className: \"text-xl font-bold text-slate-800 group-hover:text-blue-600 transition-colors duration-300\",\n                                                                    children: service.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                                    lineNumber: 144,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-slate-600 leading-relaxed\",\n                                                                    children: service.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                                    lineNumber: 147,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                            className: \"space-y-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-2xl font-bold text-blue-600\",\n                                                                                children: service.price\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                                                lineNumber: 155,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-slate-500\",\n                                                                                children: service.duration\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                                                lineNumber: 156,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                                        lineNumber: 154,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                                    lineNumber: 153,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-semibold text-slate-800\",\n                                                                            children: \"Includes:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                                            lineNumber: 161,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: service.features.map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Heart_Scissors_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                            className: \"w-4 h-4 text-green-600 flex-shrink-0\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                                                            lineNumber: 165,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-slate-600 text-sm\",\n                                                                                            children: feature\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                                                            lineNumber: 166,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, featureIndex, true, {\n                                                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                                                    lineNumber: 164,\n                                                                                    columnNumber: 31\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                                            lineNumber: 162,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                                    lineNumber: 160,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    onClick: openAppointmentModal,\n                                                                    className: \"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300\",\n                                                                    children: \"Book This Service\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, serviceIndex, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, categoryIndex, true, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/services/page.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n_s(ServicesPage, \"y1UExG1U3R/Aga6nQhJisV5OqeA=\", false, function() {\n    return [\n        _components_AppointmentProvider__WEBPACK_IMPORTED_MODULE_6__.useAppointment\n    ];\n});\n_c = ServicesPage;\nvar _c;\n$RefreshReg$(_c, \"ServicesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/services/page.tsx\n"));

/***/ })

});