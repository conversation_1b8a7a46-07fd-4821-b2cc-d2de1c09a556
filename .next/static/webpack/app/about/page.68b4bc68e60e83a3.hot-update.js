"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./app/about/page.tsx":
/*!****************************!*\
  !*** ./app/about/page.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AboutPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./components/Footer.tsx\");\n/* harmony import */ var _components_AppointmentProvider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/AppointmentProvider */ \"(app-pages-browser)/./components/AppointmentProvider.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,GraduationCap,Heart,Phone,Shield,Star,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,GraduationCap,Heart,Phone,Shield,Star,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,GraduationCap,Heart,Phone,Shield,Star,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,GraduationCap,Heart,Phone,Shield,Star,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,GraduationCap,Heart,Phone,Shield,Star,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,GraduationCap,Heart,Phone,Shield,Star,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,GraduationCap,Heart,Phone,Shield,Star,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,GraduationCap,Heart,Phone,Shield,Star,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction AboutPage() {\n    _s();\n    const { openAppointmentModal } = (0,_components_AppointmentProvider__WEBPACK_IMPORTED_MODULE_7__.useAppointment)();\n    // Handle smooth scrolling when page loads with hash\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const hash = window.location.hash;\n        if (hash) {\n            const elementId = hash.substring(1); // Remove the # symbol\n            setTimeout(()=>{\n                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.smoothScrollToElement)(elementId);\n            }, 100);\n        }\n    }, []);\n    const qualifications = [\n        {\n            title: \"Doctor of Veterinary Medicine\",\n            institution: \"University of Veterinary Sciences\",\n            year: \"2012\",\n            icon: _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            color: \"from-blue-100 to-cyan-100\"\n        },\n        {\n            title: \"Mobile Veterinary Certification\",\n            institution: \"American Mobile Veterinary Association\",\n            year: \"2014\",\n            icon: _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            color: \"from-green-100 to-emerald-100\"\n        },\n        {\n            title: \"Emergency Care Specialist\",\n            institution: \"Veterinary Emergency Board\",\n            year: \"2016\",\n            icon: _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            color: \"from-red-100 to-rose-100\"\n        }\n    ];\n    const achievements = [\n        {\n            title: \"500+ Happy Pets Treated\",\n            description: \"Successfully provided care to over 500 pets in the community\",\n            icon: _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            color: \"text-red-600\"\n        },\n        {\n            title: \"10+ Years Experience\",\n            description: \"Over a decade of dedicated veterinary practice\",\n            icon: _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            color: \"text-blue-600\"\n        },\n        {\n            title: \"24/7 Emergency Care\",\n            description: \"Round-the-clock availability for emergency situations\",\n            icon: _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            color: \"text-green-600\"\n        },\n        {\n            title: \"Mobile Service Pioneer\",\n            description: \"Leading mobile veterinary services in the region\",\n            icon: _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            color: \"text-purple-600\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative pt-20 pb-16 px-4 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-blue-600/10 via-cyan-500/5 to-slate-100/20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Meet Our Veterinarian\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-5xl lg:text-6xl font-bold text-slate-900 leading-tight\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600\",\n                                                        children: \"Nolari\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl text-slate-600 leading-relaxed\",\n                                                    children: \"A passionate veterinarian dedicated to providing exceptional mobile veterinary care. With over 10 years of experience, Nolari brings professional, compassionate care directly to your home.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: openAppointmentModal,\n                                                    size: \"lg\",\n                                                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Book Appointment\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                            lineNumber: 109,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-5 h-5 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    asChild: true,\n                                                    variant: \"outline\",\n                                                    size: \"lg\",\n                                                    className: \"border-2 border-slate-200 bg-white/70 backdrop-blur-sm text-slate-700 hover:bg-slate-50 hover:border-slate-300 px-8 py-4 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"tel:0718376311\",\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                                lineNumber: 119,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Call Now\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                                lineNumber: 120,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-3xl transform rotate-3 opacity-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-3xl shadow-2xl overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"https://images.unsplash.com/photo-1582562124811-c09040d0a901?auto=format&fit=crop&w=600&h=400\",\n                                                    alt: \"Nolari\",\n                                                    className: \"w-full h-96 object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-32 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-blue-50/30 via-white/30 to-green-50/30\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-20 space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center space-x-2 bg-gradient-to-r from-blue-100 to-green-100 rounded-2xl px-8 py-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-semibold text-slate-700\",\n                                                children: \"Qualifications\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 bg-clip-text text-transparent leading-tight\",\n                                        children: [\n                                            \"Professional\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent\",\n                                                children: \"Credentials\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                children: qualifications.map((qualification, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-8 text-center space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-20 h-20 bg-gradient-to-r \".concat(qualification.color, \" rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(qualification.icon, {\n                                                        className: \"w-10 h-10 text-slate-700\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-slate-800 group-hover:text-blue-600 transition-colors duration-300\",\n                                                            children: qualification.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-600\",\n                                                            children: qualification.institution\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: \"secondary\",\n                                                            className: \"bg-blue-100 text-blue-800\",\n                                                            children: qualification.year\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-slate-100/50 via-blue-50/50 to-green-50/50\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                            children: achievements.map((achievement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"text-center bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 rounded-3xl hover:-translate-y-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-8 space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(achievement.icon, {\n                                                className: \"w-12 h-12 mx-auto \".concat(achievement.color)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-slate-800\",\n                                                        children: achievement.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-600 text-sm\",\n                                                        children: achievement.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"about-story\",\n                className: \"py-32 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-slate-100/50 via-blue-50/50 to-green-50/50\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-16 space-y-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 bg-clip-text text-transparent\",\n                                        children: [\n                                            \"My Journey to\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent\",\n                                                children: \"Mobile Veterinary Care\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"bg-white/80 backdrop-blur-sm border-0 shadow-2xl rounded-3xl overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-64 lg:h-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: \"https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?auto=format&fit=crop&w=600&h=400\",\n                                                        alt: \"Veterinarian with pets\",\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-blue-600/20 to-green-600/20\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-12 space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-slate-800\",\n                                                        children: \"A Passion Born from Love\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4 text-slate-600 leading-relaxed\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"My journey into veterinary medicine began with a simple love for animals and a desire to make a difference in their lives. After completing my Doctor of Veterinary Medicine degree in 2012, I quickly realized that traditional clinic visits could be stressful for both pets and their families.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"This realization led me to pursue mobile veterinary services, bringing professional care directly to the comfort of your home. Over the past decade, I've had the privilege of caring for over 500 beloved pets, building lasting relationships with families throughout our community.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"My commitment to excellence drives me to stay current with the latest veterinary practices and technologies, ensuring that every pet receives the highest standard of care in the familiar environment of their own home.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4 pt-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-1\",\n                                                                children: [\n                                                                    ...Array(5)\n                                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"w-5 h-5 fill-yellow-400 text-yellow-400\"\n                                                                    }, i, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                                        lineNumber: 259,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-slate-600 font-medium\",\n                                                                children: \"Trusted by 500+ pet families\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-700\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl md:text-5xl font-bold text-white leading-tight\",\n                                    children: [\n                                        \"Ready to Experience\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\",\n                                            children: \"Compassionate Care?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-blue-100 leading-relaxed\",\n                                    children: \"Schedule your pet's appointment today and discover the convenience of professional veterinary care at home\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-6 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: openAppointmentModal,\n                                            size: \"lg\",\n                                            className: \"bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Book Appointment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-5 h-5 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            asChild: true,\n                                            size: \"lg\",\n                                            variant: \"outline\",\n                                            className: \"text-white border-white/50 bg-white/10 backdrop-blur-sm hover:bg-white hover:text-blue-600 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"tel:0718376311\",\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Call 0718376311\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n_s(AboutPage, \"y1UExG1U3R/Aga6nQhJisV5OqeA=\", false, function() {\n    return [\n        _components_AppointmentProvider__WEBPACK_IMPORTED_MODULE_7__.useAppointment\n    ];\n});\n_c = AboutPage;\nvar _c;\n$RefreshReg$(_c, \"AboutPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/about/page.tsx\n"));

/***/ })

});