"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./app/about/page.tsx":
/*!****************************!*\
  !*** ./app/about/page.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AboutPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./components/Footer.tsx\");\n/* harmony import */ var _components_AppointmentProvider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/AppointmentProvider */ \"(app-pages-browser)/./components/AppointmentProvider.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,GraduationCap,Heart,Phone,Shield,Star,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,GraduationCap,Heart,Phone,Shield,Star,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,GraduationCap,Heart,Phone,Shield,Star,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,GraduationCap,Heart,Phone,Shield,Star,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,GraduationCap,Heart,Phone,Shield,Star,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,GraduationCap,Heart,Phone,Shield,Star,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,GraduationCap,Heart,Phone,Shield,Star,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,GraduationCap,Heart,Phone,Shield,Star,Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction AboutPage() {\n    _s();\n    const { openAppointmentModal } = (0,_components_AppointmentProvider__WEBPACK_IMPORTED_MODULE_7__.useAppointment)();\n    // Handle smooth scrolling when page loads with hash\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const hash = window.location.hash;\n        if (hash) {\n            const elementId = hash.substring(1); // Remove the # symbol\n            setTimeout(()=>{\n                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.smoothScrollToElement)(elementId);\n            }, 100);\n        }\n    }, []);\n    const qualifications = [\n        {\n            title: \"Doctor of Veterinary Medicine\",\n            institution: \"University of Veterinary Sciences\",\n            year: \"2012\",\n            icon: _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            color: \"from-blue-100 to-cyan-100\"\n        },\n        {\n            title: \"Mobile Veterinary Certification\",\n            institution: \"American Mobile Veterinary Association\",\n            year: \"2014\",\n            icon: _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            color: \"from-green-100 to-emerald-100\"\n        },\n        {\n            title: \"Emergency Care Specialist\",\n            institution: \"Veterinary Emergency Board\",\n            year: \"2016\",\n            icon: _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            color: \"from-red-100 to-rose-100\"\n        }\n    ];\n    const achievements = [\n        {\n            title: \"500+ Happy Pets Treated\",\n            description: \"Successfully provided care to over 500 pets in the community\",\n            icon: _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            color: \"text-red-600\"\n        },\n        {\n            title: \"10+ Years Experience\",\n            description: \"Over a decade of dedicated veterinary practice\",\n            icon: _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            color: \"text-blue-600\"\n        },\n        {\n            title: \"24/7 Emergency Care\",\n            description: \"Round-the-clock availability for emergency situations\",\n            icon: _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            color: \"text-green-600\"\n        },\n        {\n            title: \"Mobile Service Pioneer\",\n            description: \"Leading mobile veterinary services in the region\",\n            icon: _barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            color: \"text-purple-600\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative pt-20 pb-16 px-4 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-blue-600/10 via-cyan-500/5 to-slate-100/20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Meet Our Veterinarian\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-5xl lg:text-6xl font-bold text-slate-900 leading-tight\",\n                                                    children: [\n                                                        \"Dr. Cynthia\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600\",\n                                                            children: \" Nolari\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                            lineNumber: 96,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl text-slate-600 leading-relaxed\",\n                                                    children: \"A passionate veterinarian dedicated to providing exceptional mobile veterinary care. With over 10 years of experience, Dr. Cynthia brings professional, compassionate care directly to your home.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: openAppointmentModal,\n                                                    size: \"lg\",\n                                                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Book Appointment\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-5 h-5 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                            lineNumber: 111,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    asChild: true,\n                                                    variant: \"outline\",\n                                                    size: \"lg\",\n                                                    className: \"border-2 border-slate-200 bg-white/70 backdrop-blur-sm text-slate-700 hover:bg-slate-50 hover:border-slate-300 px-8 py-4 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"tel:0718376311\",\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                                lineNumber: 120,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Call Now\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                                lineNumber: 121,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-3xl transform rotate-3 opacity-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-3xl shadow-2xl overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"https://images.unsplash.com/photo-1582562124811-c09040d0a901?auto=format&fit=crop&w=600&h=400\",\n                                                    alt: \"Dr. Cynthia Nolari\",\n                                                    className: \"w-full h-96 object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-32 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-blue-50/30 via-white/30 to-green-50/30\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-20 space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center space-x-2 bg-gradient-to-r from-blue-100 to-green-100 rounded-2xl px-8 py-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-semibold text-slate-700\",\n                                                children: \"Qualifications\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 bg-clip-text text-transparent leading-tight\",\n                                        children: [\n                                            \"Professional\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent\",\n                                                children: \"Credentials\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                children: qualifications.map((qualification, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-8 text-center space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-20 h-20 bg-gradient-to-r \".concat(qualification.color, \" rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(qualification.icon, {\n                                                        className: \"w-10 h-10 text-slate-700\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-slate-800 group-hover:text-blue-600 transition-colors duration-300\",\n                                                            children: qualification.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-600\",\n                                                            children: qualification.institution\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: \"secondary\",\n                                                            className: \"bg-blue-100 text-blue-800\",\n                                                            children: qualification.year\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-slate-100/50 via-blue-50/50 to-green-50/50\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                            children: achievements.map((achievement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"text-center bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 rounded-3xl hover:-translate-y-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-8 space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(achievement.icon, {\n                                                className: \"w-12 h-12 mx-auto \".concat(achievement.color)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-slate-800\",\n                                                        children: achievement.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-600 text-sm\",\n                                                        children: achievement.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"about-story\",\n                className: \"py-32 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-slate-100/50 via-blue-50/50 to-green-50/50\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-16 space-y-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 bg-clip-text text-transparent\",\n                                        children: [\n                                            \"My Journey to\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent\",\n                                                children: \"Mobile Veterinary Care\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"bg-white/80 backdrop-blur-sm border-0 shadow-2xl rounded-3xl overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-64 lg:h-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: \"https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?auto=format&fit=crop&w=600&h=400\",\n                                                        alt: \"Veterinarian with pets\",\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-blue-600/20 to-green-600/20\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-12 space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-slate-800\",\n                                                        children: \"A Passion Born from Love\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4 text-slate-600 leading-relaxed\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"My journey into veterinary medicine began with a simple love for animals and a desire to make a difference in their lives. After completing my Doctor of Veterinary Medicine degree in 2012, I quickly realized that traditional clinic visits could be stressful for both pets and their families.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"This realization led me to pursue mobile veterinary services, bringing professional care directly to the comfort of your home. Over the past decade, I've had the privilege of caring for over 500 beloved pets, building lasting relationships with families throughout our community.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"My commitment to excellence drives me to stay current with the latest veterinary practices and technologies, ensuring that every pet receives the highest standard of care in the familiar environment of their own home.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4 pt-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-1\",\n                                                                children: [\n                                                                    ...Array(5)\n                                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"w-5 h-5 fill-yellow-400 text-yellow-400\"\n                                                                    }, i, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-slate-600 font-medium\",\n                                                                children: \"Trusted by 500+ pet families\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-700\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl md:text-5xl font-bold text-white leading-tight\",\n                                    children: [\n                                        \"Ready to Experience\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\",\n                                            children: \"Compassionate Care?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-blue-100 leading-relaxed\",\n                                    children: \"Schedule your pet's appointment today and discover the convenience of professional veterinary care at home\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-6 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: smoothScrollToAppointmentForm,\n                                            size: \"lg\",\n                                            className: \"bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Book Appointment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-5 h-5 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            asChild: true,\n                                            size: \"lg\",\n                                            variant: \"outline\",\n                                            className: \"text-white border-white/50 bg-white/10 backdrop-blur-sm hover:bg-white hover:text-blue-600 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"tel:0718376311\",\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_GraduationCap_Heart_Phone_Shield_Star_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Call 0718376311\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/about/page.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n_s(AboutPage, \"y1UExG1U3R/Aga6nQhJisV5OqeA=\", false, function() {\n    return [\n        _components_AppointmentProvider__WEBPACK_IMPORTED_MODULE_7__.useAppointment\n    ];\n});\n_c = AboutPage;\nvar _c;\n$RefreshReg$(_c, \"AboutPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9hYm91dC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFa0M7QUFDYztBQUNnQztBQUNsQztBQUVMO0FBQ0E7QUFDeUI7QUFDZDtBQUNrRTtBQUV2RyxTQUFTaUI7O0lBQ3RCLE1BQU0sRUFBRUMsb0JBQW9CLEVBQUUsR0FBR1gsK0VBQWNBO0lBRS9DLG9EQUFvRDtJQUNwRFAsZ0RBQVNBLENBQUM7UUFDUixNQUFNbUIsT0FBT0MsT0FBT0MsUUFBUSxDQUFDRixJQUFJO1FBQ2pDLElBQUlBLE1BQU07WUFDUixNQUFNRyxZQUFZSCxLQUFLSSxTQUFTLENBQUMsSUFBSSxzQkFBc0I7WUFDM0RDLFdBQVc7Z0JBQ1RoQixpRUFBcUJBLENBQUNjO1lBQ3hCLEdBQUc7UUFDTDtJQUNGLEdBQUcsRUFBRTtJQUVMLE1BQU1HLGlCQUFpQjtRQUNyQjtZQUNFQyxPQUFPO1lBQ1BDLGFBQWE7WUFDYkMsTUFBTTtZQUNOQyxNQUFNcEIsNElBQWFBO1lBQ25CcUIsT0FBTztRQUNUO1FBQ0E7WUFDRUosT0FBTztZQUNQQyxhQUFhO1lBQ2JDLE1BQU07WUFDTkMsTUFBTW5CLDZJQUFLQTtZQUNYb0IsT0FBTztRQUNUO1FBQ0E7WUFDRUosT0FBTztZQUNQQyxhQUFhO1lBQ2JDLE1BQU07WUFDTkMsTUFBTWxCLDZJQUFLQTtZQUNYbUIsT0FBTztRQUNUO0tBQ0Q7SUFFRCxNQUFNQyxlQUFlO1FBQ25CO1lBQ0VMLE9BQU87WUFDUE0sYUFBYTtZQUNiSCxNQUFNbEIsNklBQUtBO1lBQ1htQixPQUFPO1FBQ1Q7UUFDQTtZQUNFSixPQUFPO1lBQ1BNLGFBQWE7WUFDYkgsTUFBTW5CLDZJQUFLQTtZQUNYb0IsT0FBTztRQUNUO1FBQ0E7WUFDRUosT0FBTztZQUNQTSxhQUFhO1lBQ2JILE1BQU1kLDZJQUFNQTtZQUNaZSxPQUFPO1FBQ1Q7UUFDQTtZQUNFSixPQUFPO1lBQ1BNLGFBQWE7WUFDYkgsTUFBTWIsNklBQVdBO1lBQ2pCYyxPQUFPO1FBQ1Q7S0FDRDtJQUVELHFCQUNFLDhEQUFDRztRQUFJQyxXQUFVOzswQkFDYiw4REFBQzdCLDBEQUFNQTs7Ozs7MEJBR1AsOERBQUM4QjtnQkFBUUQsV0FBVTs7a0NBQ2pCLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDdkIsNklBQUtBOzREQUFDdUIsV0FBVTs7Ozs7O3dEQUFZOzs7Ozs7OzhEQUcvQiw4REFBQ0U7b0RBQUdGLFdBQVU7O3dEQUE4RDtzRUFFMUUsOERBQUNHOzREQUFLSCxXQUFVO3NFQUEyRTs7Ozs7Ozs7Ozs7OzhEQUU3Riw4REFBQ0k7b0RBQUVKLFdBQVU7OERBQXlDOzs7Ozs7Ozs7Ozs7c0RBTXhELDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNqQyx5REFBTUE7b0RBQ0xzQyxTQUFTckI7b0RBQ1RzQixNQUFLO29EQUNMTixXQUFVOztzRUFFViw4REFBQ0c7c0VBQUs7Ozs7OztzRUFDTiw4REFBQ3pCLDZJQUFRQTs0REFBQ3NCLFdBQVU7Ozs7Ozs7Ozs7Ozs4REFFdEIsOERBQUNqQyx5REFBTUE7b0RBQ0x3QyxPQUFPO29EQUNQQyxTQUFRO29EQUNSRixNQUFLO29EQUNMTixXQUFVOzhEQUVWLDRFQUFDUzt3REFBRUMsTUFBSzt3REFBaUJWLFdBQVU7OzBFQUNqQyw4REFBQ3JCLDZJQUFLQTtnRUFBQ3FCLFdBQVU7Ozs7OzswRUFDakIsOERBQUNHOzBFQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNZCw4REFBQ0o7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7Ozs7O3NEQUNmLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNXO29EQUNDQyxLQUFJO29EQUNKQyxLQUFJO29EQUNKYixXQUFVOzs7Ozs7OERBRVosOERBQUNEO29EQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRekIsOERBQUNDO2dCQUFRRCxXQUFVOztrQ0FDakIsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUN6Qiw0SUFBYUE7Z0RBQUN5QixXQUFVOzs7Ozs7MERBQ3pCLDhEQUFDRztnREFBS0gsV0FBVTswREFBdUM7Ozs7Ozs7Ozs7OztrREFHekQsOERBQUNjO3dDQUFHZCxXQUFVOzs0Q0FBdUk7MERBRW5KLDhEQUFDZTs7Ozs7MERBQ0QsOERBQUNaO2dEQUFLSCxXQUFVOzBEQUE0RTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU1oRyw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ1pULGVBQWV5QixHQUFHLENBQUMsQ0FBQ0MsZUFBZUMsc0JBQ2xDLDhEQUFDbEQscURBQUlBO3dDQUVIZ0MsV0FBVTtrREFFViw0RUFBQy9CLDREQUFXQTs0Q0FBQytCLFdBQVU7OzhEQUNyQiw4REFBQ0Q7b0RBQUlDLFdBQVcsOEJBQWtELE9BQXBCaUIsY0FBY3JCLEtBQUssRUFBQzs4REFDaEUsNEVBQUNxQixjQUFjdEIsSUFBSTt3REFBQ0ssV0FBVTs7Ozs7Ozs7Ozs7OERBR2hDLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNtQjs0REFBR25CLFdBQVU7c0VBQ1hpQixjQUFjekIsS0FBSzs7Ozs7O3NFQUV0Qiw4REFBQ1k7NERBQUVKLFdBQVU7c0VBQWtCaUIsY0FBY3hCLFdBQVc7Ozs7OztzRUFDeEQsOERBQUN2Qix1REFBS0E7NERBQUNzQyxTQUFROzREQUFZUixXQUFVO3NFQUNsQ2lCLGNBQWN2QixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7dUNBZHBCd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBeUJmLDhEQUFDakI7Z0JBQVFELFdBQVU7O2tDQUNqQiw4REFBQ0Q7d0JBQUlDLFdBQVU7Ozs7OztrQ0FDZiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVO3NDQUNaSCxhQUFhbUIsR0FBRyxDQUFDLENBQUNJLGFBQWFGLHNCQUM5Qiw4REFBQ2xELHFEQUFJQTtvQ0FFSGdDLFdBQVU7OENBRVYsNEVBQUMvQiw0REFBV0E7d0NBQUMrQixXQUFVOzswREFDckIsOERBQUNvQixZQUFZekIsSUFBSTtnREFBQ0ssV0FBVyxxQkFBdUMsT0FBbEJvQixZQUFZeEIsS0FBSzs7Ozs7OzBEQUNuRSw4REFBQ0c7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDbUI7d0RBQUduQixXQUFVO2tFQUFvQ29CLFlBQVk1QixLQUFLOzs7Ozs7a0VBQ25FLDhEQUFDWTt3REFBRUosV0FBVTtrRUFBMEJvQixZQUFZdEIsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7O21DQVA3RG9COzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBaUJmLDhEQUFDakI7Z0JBQVFvQixJQUFHO2dCQUFjckIsV0FBVTs7a0NBQ2xDLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ2M7d0NBQUdkLFdBQVU7OzRDQUF5SDswREFFckksOERBQUNlOzs7OzswREFDRCw4REFBQ1o7Z0RBQUtILFdBQVU7MERBQTRFOzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNaEcsOERBQUNoQyxxREFBSUE7b0NBQUNnQyxXQUFVOzhDQUNkLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ1c7d0RBQ0NDLEtBQUk7d0RBQ0pDLEtBQUk7d0RBQ0piLFdBQVU7Ozs7OztrRUFFWiw4REFBQ0Q7d0RBQUlDLFdBQVU7Ozs7Ozs7Ozs7OzswREFHakIsOERBQUMvQiw0REFBV0E7Z0RBQUMrQixXQUFVOztrRUFDckIsOERBQUNtQjt3REFBR25CLFdBQVU7a0VBQW9DOzs7Ozs7a0VBRWxELDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNJOzBFQUFFOzs7Ozs7MEVBS0gsOERBQUNBOzBFQUFFOzs7Ozs7MEVBS0gsOERBQUNBOzBFQUFFOzs7Ozs7Ozs7Ozs7a0VBTUwsOERBQUNMO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQ1o7dUVBQUlzQixNQUFNO2lFQUFHLENBQUNOLEdBQUcsQ0FBQyxDQUFDTyxHQUFHQyxrQkFDckIsOERBQUM1Qyw2SUFBSUE7d0VBQVNvQixXQUFVO3VFQUFid0I7Ozs7Ozs7Ozs7MEVBR2YsOERBQUNyQjtnRUFBS0gsV0FBVTswRUFBNkI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBVTNELDhEQUFDQztnQkFBUUQsV0FBVTs7a0NBQ2pCLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDYztvQ0FBR2QsV0FBVTs7d0NBQTBEO3NEQUV0RSw4REFBQ2U7Ozs7O3NEQUNELDhEQUFDWjs0Q0FBS0gsV0FBVTtzREFBK0U7Ozs7Ozs7Ozs7Ozs4Q0FLakcsOERBQUNJO29DQUFFSixXQUFVOzhDQUF3Qzs7Ozs7OzhDQUlyRCw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDakMseURBQU1BOzRDQUNMc0MsU0FBU29COzRDQUNUbkIsTUFBSzs0Q0FDTE4sV0FBVTs7OERBRVYsOERBQUNHOzhEQUFLOzs7Ozs7OERBQ04sOERBQUN6Qiw2SUFBUUE7b0RBQUNzQixXQUFVOzs7Ozs7Ozs7Ozs7c0RBRXRCLDhEQUFDakMseURBQU1BOzRDQUNMd0MsT0FBTzs0Q0FDUEQsTUFBSzs0Q0FDTEUsU0FBUTs0Q0FDUlIsV0FBVTtzREFFViw0RUFBQ1M7Z0RBQUVDLE1BQUs7Z0RBQWlCVixXQUFVOztrRUFDakMsOERBQUNyQiw2SUFBS0E7d0RBQUNxQixXQUFVOzs7Ozs7a0VBQ2pCLDhEQUFDRztrRUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRbEIsOERBQUMvQiwwREFBTUE7Ozs7Ozs7Ozs7O0FBR2I7R0FoVHdCVzs7UUFDV1YsMkVBQWNBOzs7S0FEekJVIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC9hYm91dC9wYWdlLnRzeD84MDZkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbic7XG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnO1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmFkZ2UnO1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcbmltcG9ydCBIZWFkZXIgZnJvbSAnQC9jb21wb25lbnRzL0hlYWRlcic7XG5pbXBvcnQgRm9vdGVyIGZyb20gJ0AvY29tcG9uZW50cy9Gb290ZXInO1xuaW1wb3J0IHsgdXNlQXBwb2ludG1lbnQgfSBmcm9tICdAL2NvbXBvbmVudHMvQXBwb2ludG1lbnRQcm92aWRlcic7XG5pbXBvcnQgeyBzbW9vdGhTY3JvbGxUb0VsZW1lbnQgfSBmcm9tICdAL2xpYi91dGlscyc7XG5pbXBvcnQgeyBHcmFkdWF0aW9uQ2FwLCBBd2FyZCwgSGVhcnQsIFVzZXJzLCBDYWxlbmRhciwgTWFwUGluLCBQaG9uZSwgU3RhciwgU2hpZWxkLCBTdGV0aG9zY29wZSB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFib3V0UGFnZSgpIHtcbiAgY29uc3QgeyBvcGVuQXBwb2ludG1lbnRNb2RhbCB9ID0gdXNlQXBwb2ludG1lbnQoKTtcblxuICAvLyBIYW5kbGUgc21vb3RoIHNjcm9sbGluZyB3aGVuIHBhZ2UgbG9hZHMgd2l0aCBoYXNoXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFzaCA9IHdpbmRvdy5sb2NhdGlvbi5oYXNoO1xuICAgIGlmIChoYXNoKSB7XG4gICAgICBjb25zdCBlbGVtZW50SWQgPSBoYXNoLnN1YnN0cmluZygxKTsgLy8gUmVtb3ZlIHRoZSAjIHN5bWJvbFxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIHNtb290aFNjcm9sbFRvRWxlbWVudChlbGVtZW50SWQpO1xuICAgICAgfSwgMTAwKTtcbiAgICB9XG4gIH0sIFtdKTtcblxuICBjb25zdCBxdWFsaWZpY2F0aW9ucyA9IFtcbiAgICB7XG4gICAgICB0aXRsZTogXCJEb2N0b3Igb2YgVmV0ZXJpbmFyeSBNZWRpY2luZVwiLFxuICAgICAgaW5zdGl0dXRpb246IFwiVW5pdmVyc2l0eSBvZiBWZXRlcmluYXJ5IFNjaWVuY2VzXCIsXG4gICAgICB5ZWFyOiBcIjIwMTJcIixcbiAgICAgIGljb246IEdyYWR1YXRpb25DYXAsXG4gICAgICBjb2xvcjogXCJmcm9tLWJsdWUtMTAwIHRvLWN5YW4tMTAwXCJcbiAgICB9LFxuICAgIHtcbiAgICAgIHRpdGxlOiBcIk1vYmlsZSBWZXRlcmluYXJ5IENlcnRpZmljYXRpb25cIixcbiAgICAgIGluc3RpdHV0aW9uOiBcIkFtZXJpY2FuIE1vYmlsZSBWZXRlcmluYXJ5IEFzc29jaWF0aW9uXCIsXG4gICAgICB5ZWFyOiBcIjIwMTRcIixcbiAgICAgIGljb246IEF3YXJkLFxuICAgICAgY29sb3I6IFwiZnJvbS1ncmVlbi0xMDAgdG8tZW1lcmFsZC0xMDBcIlxuICAgIH0sXG4gICAge1xuICAgICAgdGl0bGU6IFwiRW1lcmdlbmN5IENhcmUgU3BlY2lhbGlzdFwiLFxuICAgICAgaW5zdGl0dXRpb246IFwiVmV0ZXJpbmFyeSBFbWVyZ2VuY3kgQm9hcmRcIixcbiAgICAgIHllYXI6IFwiMjAxNlwiLFxuICAgICAgaWNvbjogSGVhcnQsXG4gICAgICBjb2xvcjogXCJmcm9tLXJlZC0xMDAgdG8tcm9zZS0xMDBcIlxuICAgIH1cbiAgXTtcblxuICBjb25zdCBhY2hpZXZlbWVudHMgPSBbXG4gICAge1xuICAgICAgdGl0bGU6IFwiNTAwKyBIYXBweSBQZXRzIFRyZWF0ZWRcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIlN1Y2Nlc3NmdWxseSBwcm92aWRlZCBjYXJlIHRvIG92ZXIgNTAwIHBldHMgaW4gdGhlIGNvbW11bml0eVwiLFxuICAgICAgaWNvbjogSGVhcnQsXG4gICAgICBjb2xvcjogXCJ0ZXh0LXJlZC02MDBcIlxuICAgIH0sXG4gICAge1xuICAgICAgdGl0bGU6IFwiMTArIFllYXJzIEV4cGVyaWVuY2VcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIk92ZXIgYSBkZWNhZGUgb2YgZGVkaWNhdGVkIHZldGVyaW5hcnkgcHJhY3RpY2VcIixcbiAgICAgIGljb246IEF3YXJkLFxuICAgICAgY29sb3I6IFwidGV4dC1ibHVlLTYwMFwiXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXRsZTogXCIyNC83IEVtZXJnZW5jeSBDYXJlXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCJSb3VuZC10aGUtY2xvY2sgYXZhaWxhYmlsaXR5IGZvciBlbWVyZ2VuY3kgc2l0dWF0aW9uc1wiLFxuICAgICAgaWNvbjogU2hpZWxkLFxuICAgICAgY29sb3I6IFwidGV4dC1ncmVlbi02MDBcIlxuICAgIH0sXG4gICAge1xuICAgICAgdGl0bGU6IFwiTW9iaWxlIFNlcnZpY2UgUGlvbmVlclwiLFxuICAgICAgZGVzY3JpcHRpb246IFwiTGVhZGluZyBtb2JpbGUgdmV0ZXJpbmFyeSBzZXJ2aWNlcyBpbiB0aGUgcmVnaW9uXCIsXG4gICAgICBpY29uOiBTdGV0aG9zY29wZSxcbiAgICAgIGNvbG9yOiBcInRleHQtcHVycGxlLTYwMFwiXG4gICAgfVxuICBdO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTUwIHZpYS1waW5rLTUwIHRvLWdyYXktNTBcIj5cbiAgICAgIDxIZWFkZXIgLz5cblxuICAgICAgey8qIEhlcm8gU2VjdGlvbiAqL31cbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInJlbGF0aXZlIHB0LTIwIHBiLTE2IHB4LTQgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNjAwLzEwIHZpYS1jeWFuLTUwMC81IHRvLXNsYXRlLTEwMC8yMFwiPjwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIG1heC13LTd4bCBteC1hdXRvXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGxnOmdyaWQtY29scy0yIGdhcC0xMiBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS04XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMCBweC00IHB5LTIgcm91bmRlZC1mdWxsIHRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgIDxIZWFydCBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgIE1lZXQgT3VyIFZldGVyaW5hcmlhblxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTV4bCBsZzp0ZXh0LTZ4bCBmb250LWJvbGQgdGV4dC1zbGF0ZS05MDAgbGVhZGluZy10aWdodFwiPlxuICAgICAgICAgICAgICAgICAgRHIuIEN5bnRoaWFcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtdHJhbnNwYXJlbnQgYmctY2xpcC10ZXh0IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMCB0by1jeWFuLTYwMFwiPiBOb2xhcmk8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtc2xhdGUtNjAwIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICAgICAgQSBwYXNzaW9uYXRlIHZldGVyaW5hcmlhbiBkZWRpY2F0ZWQgdG8gcHJvdmlkaW5nIGV4Y2VwdGlvbmFsIG1vYmlsZSB2ZXRlcmluYXJ5IGNhcmUuXG4gICAgICAgICAgICAgICAgICBXaXRoIG92ZXIgMTAgeWVhcnMgb2YgZXhwZXJpZW5jZSwgRHIuIEN5bnRoaWEgYnJpbmdzIHByb2Zlc3Npb25hbCwgY29tcGFzc2lvbmF0ZSBjYXJlIGRpcmVjdGx5IHRvIHlvdXIgaG9tZS5cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtNlwiPlxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e29wZW5BcHBvaW50bWVudE1vZGFsfVxuICAgICAgICAgICAgICAgICAgc2l6ZT1cImxnXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMCB0by1ibHVlLTcwMCBob3Zlcjpmcm9tLWJsdWUtNzAwIGhvdmVyOnRvLWJsdWUtODAwIHRleHQtd2hpdGUgcHgtOCBweS00IHJvdW5kZWQtMnhsIHNoYWRvdy14bCBob3ZlcjpzaGFkb3ctMnhsIHRyYW5zZm9ybSBob3ZlcjpzY2FsZS0xMDUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj5Cb29rIEFwcG9pbnRtZW50PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cInctNSBoLTUgbWwtMlwiIC8+XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgYXNDaGlsZFxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgc2l6ZT1cImxnXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci0yIGJvcmRlci1zbGF0ZS0yMDAgYmctd2hpdGUvNzAgYmFja2Ryb3AtYmx1ci1zbSB0ZXh0LXNsYXRlLTcwMCBob3ZlcjpiZy1zbGF0ZS01MCBob3Zlcjpib3JkZXItc2xhdGUtMzAwIHB4LTggcHktNCByb3VuZGVkLTJ4bCBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIHRyYW5zZm9ybSBob3ZlcjpzY2FsZS0xMDUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8YSBocmVmPVwidGVsOjA3MTgzNzYzMTFcIiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPFBob25lIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj5DYWxsIE5vdzwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNDAwIHRvLWN5YW4tNDAwIHJvdW5kZWQtM3hsIHRyYW5zZm9ybSByb3RhdGUtMyBvcGFjaXR5LTIwXCI+PC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgYmctd2hpdGUgcm91bmRlZC0zeGwgc2hhZG93LTJ4bCBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICBzcmM9XCJodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU4MjU2MjEyNDgxMS1jMDkwNDBkMGE5MDE/YXV0bz1mb3JtYXQmZml0PWNyb3Amdz02MDAmaD00MDBcIlxuICAgICAgICAgICAgICAgICAgYWx0PVwiRHIuIEN5bnRoaWEgTm9sYXJpXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLTk2IG9iamVjdC1jb3ZlclwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tdCBmcm9tLWJsYWNrLzIwIHRvLXRyYW5zcGFyZW50XCI+PC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICB7LyogUXVhbGlmaWNhdGlvbnMgU2VjdGlvbiAqL31cbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInB5LTMyIHJlbGF0aXZlIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNTAvMzAgdmlhLXdoaXRlLzMwIHRvLWdyZWVuLTUwLzMwXCI+PC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgbWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTIwIHNwYWNlLXktNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTEwMCB0by1ncmVlbi0xMDAgcm91bmRlZC0yeGwgcHgtOCBweS00XCI+XG4gICAgICAgICAgICAgIDxHcmFkdWF0aW9uQ2FwIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1ibHVlLTYwMFwiIC8+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXNsYXRlLTcwMFwiPlF1YWxpZmljYXRpb25zPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBtZDp0ZXh0LTV4bCBmb250LWJvbGQgYmctZ3JhZGllbnQtdG8tciBmcm9tLXNsYXRlLTkwMCB2aWEtYmx1ZS05MDAgdG8tc2xhdGUtOTAwIGJnLWNsaXAtdGV4dCB0ZXh0LXRyYW5zcGFyZW50IGxlYWRpbmctdGlnaHRcIj5cbiAgICAgICAgICAgICAgUHJvZmVzc2lvbmFsXG4gICAgICAgICAgICAgIDxiciAvPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdG8tZ3JlZW4tNjAwIGJnLWNsaXAtdGV4dCB0ZXh0LXRyYW5zcGFyZW50XCI+XG4gICAgICAgICAgICAgICAgQ3JlZGVudGlhbHNcbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMyBnYXAtOFwiPlxuICAgICAgICAgICAge3F1YWxpZmljYXRpb25zLm1hcCgocXVhbGlmaWNhdGlvbiwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgPENhcmRcbiAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImdyb3VwIHJlbGF0aXZlIG92ZXJmbG93LWhpZGRlbiBiZy13aGl0ZS83MCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlci0wIHNoYWRvdy1sZyBob3ZlcjpzaGFkb3ctMnhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMCBlYXNlLW91dCBob3ZlcjotdHJhbnNsYXRlLXktMiByb3VuZGVkLTN4bFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC04IHRleHQtY2VudGVyIHNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B3LTIwIGgtMjAgYmctZ3JhZGllbnQtdG8tciAke3F1YWxpZmljYXRpb24uY29sb3J9IHJvdW5kZWQtMnhsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gc2hhZG93LWxnIGdyb3VwLWhvdmVyOnNjYWxlLTExMCB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDBgfT5cbiAgICAgICAgICAgICAgICAgICAgPHF1YWxpZmljYXRpb24uaWNvbiBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgdGV4dC1zbGF0ZS03MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LXNsYXRlLTgwMCBncm91cC1ob3Zlcjp0ZXh0LWJsdWUtNjAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtxdWFsaWZpY2F0aW9uLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNsYXRlLTYwMFwiPntxdWFsaWZpY2F0aW9uLmluc3RpdHV0aW9ufTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJzZWNvbmRhcnlcIiBjbGFzc05hbWU9XCJiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAge3F1YWxpZmljYXRpb24ueWVhcn1cbiAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgey8qIEFjaGlldmVtZW50cyBTZWN0aW9uICovfVxuICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicHktMjAgcmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1iciBmcm9tLXNsYXRlLTEwMC81MCB2aWEtYmx1ZS01MC81MCB0by1ncmVlbi01MC81MFwiPjwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIG1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIG1kOmdyaWQtY29scy00IGdhcC04XCI+XG4gICAgICAgICAgICB7YWNoaWV2ZW1lbnRzLm1hcCgoYWNoaWV2ZW1lbnQsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgIDxDYXJkXG4gICAgICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBiZy13aGl0ZS83MCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlci0wIHNoYWRvdy1sZyBob3ZlcjpzaGFkb3cteGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIHJvdW5kZWQtM3hsIGhvdmVyOi10cmFuc2xhdGUteS0xXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTggc3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICA8YWNoaWV2ZW1lbnQuaWNvbiBjbGFzc05hbWU9e2B3LTEyIGgtMTIgbXgtYXV0byAke2FjaGlldmVtZW50LmNvbG9yfWB9IC8+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgdGV4dC1zbGF0ZS04MDBcIj57YWNoaWV2ZW1lbnQudGl0bGV9PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbGF0ZS02MDAgdGV4dC1zbVwiPnthY2hpZXZlbWVudC5kZXNjcmlwdGlvbn08L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L3NlY3Rpb24+XG5cbiAgICAgIHsvKiBTdG9yeSBTZWN0aW9uICovfVxuICAgICAgPHNlY3Rpb24gaWQ9XCJhYm91dC1zdG9yeVwiIGNsYXNzTmFtZT1cInB5LTMyIHJlbGF0aXZlIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1zbGF0ZS0xMDAvNTAgdmlhLWJsdWUtNTAvNTAgdG8tZ3JlZW4tNTAvNTBcIj48L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNHhsIG14LWF1dG9cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItMTYgc3BhY2UteS02XCI+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBtZDp0ZXh0LTV4bCBmb250LWJvbGQgYmctZ3JhZGllbnQtdG8tciBmcm9tLXNsYXRlLTkwMCB2aWEtYmx1ZS05MDAgdG8tc2xhdGUtOTAwIGJnLWNsaXAtdGV4dCB0ZXh0LXRyYW5zcGFyZW50XCI+XG4gICAgICAgICAgICAgICAgTXkgSm91cm5leSB0b1xuICAgICAgICAgICAgICAgIDxiciAvPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMCB0by1ncmVlbi02MDAgYmctY2xpcC10ZXh0IHRleHQtdHJhbnNwYXJlbnRcIj5cbiAgICAgICAgICAgICAgICAgIE1vYmlsZSBWZXRlcmluYXJ5IENhcmVcbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiYmctd2hpdGUvODAgYmFja2Ryb3AtYmx1ci1zbSBib3JkZXItMCBzaGFkb3ctMnhsIHJvdW5kZWQtM3hsIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGgtNjQgbGc6aC1mdWxsXCI+XG4gICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgIHNyYz1cImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTM1MjY4NjQ3Njc3LTMwMGRiZjNkNzhkMT9hdXRvPWZvcm1hdCZmaXQ9Y3JvcCZ3PTYwMCZoPTQwMFwiXG4gICAgICAgICAgICAgICAgICAgIGFsdD1cIlZldGVyaW5hcmlhbiB3aXRoIHBldHNcIlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlclwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMC8yMCB0by1ncmVlbi02MDAvMjBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTEyIHNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXNsYXRlLTgwMFwiPkEgUGFzc2lvbiBCb3JuIGZyb20gTG92ZTwvaDM+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00IHRleHQtc2xhdGUtNjAwIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICAgICAgICA8cD5cbiAgICAgICAgICAgICAgICAgICAgICBNeSBqb3VybmV5IGludG8gdmV0ZXJpbmFyeSBtZWRpY2luZSBiZWdhbiB3aXRoIGEgc2ltcGxlIGxvdmUgZm9yIGFuaW1hbHMgYW5kIGEgZGVzaXJlIHRvIG1ha2UgYSBkaWZmZXJlbmNlIGluIHRoZWlyIGxpdmVzLlxuICAgICAgICAgICAgICAgICAgICAgIEFmdGVyIGNvbXBsZXRpbmcgbXkgRG9jdG9yIG9mIFZldGVyaW5hcnkgTWVkaWNpbmUgZGVncmVlIGluIDIwMTIsIEkgcXVpY2tseSByZWFsaXplZCB0aGF0IHRyYWRpdGlvbmFsIGNsaW5pYyB2aXNpdHMgY291bGQgYmUgc3RyZXNzZnVsIGZvciBib3RoIHBldHMgYW5kIHRoZWlyIGZhbWlsaWVzLlxuICAgICAgICAgICAgICAgICAgICA8L3A+XG5cbiAgICAgICAgICAgICAgICAgICAgPHA+XG4gICAgICAgICAgICAgICAgICAgICAgVGhpcyByZWFsaXphdGlvbiBsZWQgbWUgdG8gcHVyc3VlIG1vYmlsZSB2ZXRlcmluYXJ5IHNlcnZpY2VzLCBicmluZ2luZyBwcm9mZXNzaW9uYWwgY2FyZSBkaXJlY3RseSB0byB0aGUgY29tZm9ydCBvZiB5b3VyIGhvbWUuXG4gICAgICAgICAgICAgICAgICAgICAgT3ZlciB0aGUgcGFzdCBkZWNhZGUsIEkndmUgaGFkIHRoZSBwcml2aWxlZ2Ugb2YgY2FyaW5nIGZvciBvdmVyIDUwMCBiZWxvdmVkIHBldHMsIGJ1aWxkaW5nIGxhc3RpbmcgcmVsYXRpb25zaGlwcyB3aXRoIGZhbWlsaWVzIHRocm91Z2hvdXQgb3VyIGNvbW11bml0eS5cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuXG4gICAgICAgICAgICAgICAgICAgIDxwPlxuICAgICAgICAgICAgICAgICAgICAgIE15IGNvbW1pdG1lbnQgdG8gZXhjZWxsZW5jZSBkcml2ZXMgbWUgdG8gc3RheSBjdXJyZW50IHdpdGggdGhlIGxhdGVzdCB2ZXRlcmluYXJ5IHByYWN0aWNlcyBhbmQgdGVjaG5vbG9naWVzLFxuICAgICAgICAgICAgICAgICAgICAgIGVuc3VyaW5nIHRoYXQgZXZlcnkgcGV0IHJlY2VpdmVzIHRoZSBoaWdoZXN0IHN0YW5kYXJkIG9mIGNhcmUgaW4gdGhlIGZhbWlsaWFyIGVudmlyb25tZW50IG9mIHRoZWlyIG93biBob21lLlxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTQgcHQtNFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAge1suLi5BcnJheSg1KV0ubWFwKChfLCBpKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8U3RhciBrZXk9e2l9IGNsYXNzTmFtZT1cInctNSBoLTUgZmlsbC15ZWxsb3ctNDAwIHRleHQteWVsbG93LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNsYXRlLTYwMCBmb250LW1lZGl1bVwiPlRydXN0ZWQgYnkgNTAwKyBwZXQgZmFtaWxpZXM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L3NlY3Rpb24+XG5cbiAgICAgIHsvKiBDb250YWN0IENUQSBTZWN0aW9uICovfVxuICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicHktMjAgcmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdmlhLXB1cnBsZS02MDAgdG8tYmx1ZS03MDBcIj48L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWJsYWNrLzIwXCI+PC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgbWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LTggdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTR4bCBteC1hdXRvIHNwYWNlLXktOFwiPlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtNHhsIG1kOnRleHQtNXhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIGxlYWRpbmctdGlnaHRcIj5cbiAgICAgICAgICAgICAgUmVhZHkgdG8gRXhwZXJpZW5jZVxuICAgICAgICAgICAgICA8YnIgLz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLXllbGxvdy0zMDAgdG8tb3JhbmdlLTMwMCBiZy1jbGlwLXRleHQgdGV4dC10cmFuc3BhcmVudFwiPlxuICAgICAgICAgICAgICAgIENvbXBhc3Npb25hdGUgQ2FyZT9cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9oMj5cblxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCB0ZXh0LWJsdWUtMTAwIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICBTY2hlZHVsZSB5b3VyIHBldCdzIGFwcG9pbnRtZW50IHRvZGF5IGFuZCBkaXNjb3ZlciB0aGUgY29udmVuaWVuY2Ugb2YgcHJvZmVzc2lvbmFsIHZldGVyaW5hcnkgY2FyZSBhdCBob21lXG4gICAgICAgICAgICA8L3A+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtNiBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17c21vb3RoU2Nyb2xsVG9BcHBvaW50bWVudEZvcm19XG4gICAgICAgICAgICAgICAgc2l6ZT1cImxnXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZSB0ZXh0LWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNTAgcHgtOCBweS00IHJvdW5kZWQtMnhsIHNoYWRvdy14bCBob3ZlcjpzaGFkb3ctMnhsIHRyYW5zZm9ybSBob3ZlcjpzY2FsZS0xMDUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxzcGFuPkJvb2sgQXBwb2ludG1lbnQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cInctNSBoLTUgbWwtMlwiIC8+XG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgYXNDaGlsZFxuICAgICAgICAgICAgICAgIHNpemU9XCJsZ1wiXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgYm9yZGVyLXdoaXRlLzUwIGJnLXdoaXRlLzEwIGJhY2tkcm9wLWJsdXItc20gaG92ZXI6Ymctd2hpdGUgaG92ZXI6dGV4dC1ibHVlLTYwMCBweC04IHB5LTQgcm91bmRlZC0yeGwgc2hhZG93LXhsIGhvdmVyOnNoYWRvdy0yeGwgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGEgaHJlZj1cInRlbDowNzE4Mzc2MzExXCIgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICA8UGhvbmUgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj5DYWxsIDA3MTgzNzYzMTE8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgPEZvb3RlciAvPlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsIkJ1dHRvbiIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkJhZGdlIiwiSGVhZGVyIiwiRm9vdGVyIiwidXNlQXBwb2ludG1lbnQiLCJzbW9vdGhTY3JvbGxUb0VsZW1lbnQiLCJHcmFkdWF0aW9uQ2FwIiwiQXdhcmQiLCJIZWFydCIsIkNhbGVuZGFyIiwiUGhvbmUiLCJTdGFyIiwiU2hpZWxkIiwiU3RldGhvc2NvcGUiLCJBYm91dFBhZ2UiLCJvcGVuQXBwb2ludG1lbnRNb2RhbCIsImhhc2giLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImVsZW1lbnRJZCIsInN1YnN0cmluZyIsInNldFRpbWVvdXQiLCJxdWFsaWZpY2F0aW9ucyIsInRpdGxlIiwiaW5zdGl0dXRpb24iLCJ5ZWFyIiwiaWNvbiIsImNvbG9yIiwiYWNoaWV2ZW1lbnRzIiwiZGVzY3JpcHRpb24iLCJkaXYiLCJjbGFzc05hbWUiLCJzZWN0aW9uIiwiaDEiLCJzcGFuIiwicCIsIm9uQ2xpY2siLCJzaXplIiwiYXNDaGlsZCIsInZhcmlhbnQiLCJhIiwiaHJlZiIsImltZyIsInNyYyIsImFsdCIsImgyIiwiYnIiLCJtYXAiLCJxdWFsaWZpY2F0aW9uIiwiaW5kZXgiLCJoMyIsImFjaGlldmVtZW50IiwiaWQiLCJBcnJheSIsIl8iLCJpIiwic21vb3RoU2Nyb2xsVG9BcHBvaW50bWVudEZvcm0iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/about/page.tsx\n"));

/***/ })

});