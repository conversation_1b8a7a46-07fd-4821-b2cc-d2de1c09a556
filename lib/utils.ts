import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function smoothScrollToElement(elementId: string, offset: number = 80) {
  const element = document.getElementById(elementId)
  if (element) {
    const elementPosition = element.getBoundingClientRect().top + window.pageYOffset
    const offsetPosition = elementPosition - offset

    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth'
    })
  }
}

export function smoothScrollToAppointmentForm() {
  // Always scroll to the appointment form on the current page
  smoothScrollToElement('appointment-form')
}

export function smoothScrollToSection(sectionId: string, targetPath?: string) {
  // If we need to navigate to a different page first
  if (targetPath && window.location.pathname !== targetPath) {
    window.location.href = targetPath
    return
  }

  // If we're already on the correct page, just scroll
  smoothScrollToElement(sectionId)
}

export function handleMenuNavigation(href: string) {
  // Use normal navigation without hash symbols
  window.location.href = href
}
