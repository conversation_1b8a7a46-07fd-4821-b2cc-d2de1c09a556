'use client';

import { createContext, useContext, useState, ReactNode } from 'react';
import AppointmentModal from './AppointmentModal';

interface AppointmentContextType {
  openAppointmentModal: () => void;
  closeAppointmentModal: () => void;
  isModalOpen: boolean;
}

const AppointmentContext = createContext<AppointmentContextType | undefined>(undefined);

export function useAppointment() {
  const context = useContext(AppointmentContext);
  if (context === undefined) {
    throw new Error('useAppointment must be used within an AppointmentProvider');
  }
  return context;
}

interface AppointmentProviderProps {
  children: ReactNode;
}

export function AppointmentProvider({ children }: AppointmentProviderProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openAppointmentModal = () => setIsModalOpen(true);
  const closeAppointmentModal = () => setIsModalOpen(false);

  return (
    <AppointmentContext.Provider
      value={{
        openAppointmentModal,
        closeAppointmentModal,
        isModalOpen,
      }}
    >
      {children}
      <AppointmentModal 
        isOpen={isModalOpen} 
        onClose={closeAppointmentModal} 
      />
    </AppointmentContext.Provider>
  );
}
