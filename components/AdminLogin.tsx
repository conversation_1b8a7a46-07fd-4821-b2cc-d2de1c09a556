
import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';

interface AdminLoginProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const AdminLogin = ({ open, onOpenChange }: AdminLoginProps) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Simple demo login - in production you'd validate against a backend
    if (username === 'admin' && password === 'cvets123') {
      localStorage.setItem('cvets_admin', 'true');
      toast({
        title: "Login successful",
        description: "Welcome back, admin!",
      });
      onOpenChange(false);
      setUsername('');
      setPassword('');
    } else {
      toast({
        title: "Login failed",
        description: "Invalid credentials. Please try again.",
        variant: "destructive",
      });
    }
    
    setIsLoading(false);
  };

  const handleLogout = () => {
    localStorage.removeItem('cvets_admin');
    toast({
      title: "Logged out",
      description: "You have been logged out successfully.",
    });
    onOpenChange(false);
  };

  const isLoggedIn = typeof window !== 'undefined' && localStorage.getItem('cvets_admin') === 'true';

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {isLoggedIn ? 'Admin Panel' : 'Admin Login'}
          </DialogTitle>
        </DialogHeader>
        
        {isLoggedIn ? (
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              You are logged in as administrator.
            </p>
            <div className="flex space-x-2">
              <Button
                onClick={() => window.location.href = '/manage-blogs'}
                className="flex-1"
              >
                Manage Blogs
              </Button>
              <Button
                onClick={() => window.location.href = '/manage-products'}
                className="flex-1"
                variant="outline"
              >
                Manage Products
              </Button>
            </div>
            <Button 
              onClick={handleLogout}
              variant="destructive"
              className="w-full"
            >
              Logout
            </Button>
          </div>
        ) : (
          <form onSubmit={handleLogin} className="space-y-4">
            <div>
              <Input
                type="text"
                placeholder="Username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                required
              />
            </div>
            <div>
              <Input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>
            <Button 
              type="submit" 
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? 'Logging in...' : 'Login'}
            </Button>
            <p className="text-xs text-muted-foreground text-center">
              Demo: admin / cvets123
            </p>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default AdminLogin;
