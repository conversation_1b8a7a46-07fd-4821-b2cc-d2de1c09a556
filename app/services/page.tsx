'use client';

import { useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useAppointment } from '@/components/AppointmentProvider';
import { smoothScrollToElement } from '@/lib/utils';
import { Stethoscope, Heart, Shield, Clock, MapPin, CreditCard, CheckCircle, Scissors, Plane, Bug } from 'lucide-react';

export default function ServicesPage() {
  const { openAppointmentModal } = useAppointment();

  // Handle smooth scrolling when page loads with hash
  useEffect(() => {
    const hash = window.location.hash;
    if (hash) {
      const elementId = hash.substring(1); // Remove the # symbol
      setTimeout(() => {
        smoothScrollToElement(elementId);
      }, 100);
    }
  }, []);

  const services = [
    {
      category: "Medical Care",
      gradient: "from-blue-50 to-cyan-50",
      icon: Stethoscope,
      items: [
        {
          name: "Health Check-ups",
          description: "Comprehensive physical examinations to assess your pet's overall health and detect early signs of illness.",
          duration: "45-60 minutes",
          features: ["Physical examination", "Weight and vital signs", "Health assessment", "Preventive care recommendations"]
        },
        {
          name: "Diagnostic Testing",
          description: "Advanced diagnostic services including blood work, urinalysis, and other laboratory tests.",
          duration: "30-45 minutes",
          features: ["Blood chemistry panels", "Complete blood count", "Urinalysis", "Parasite screening"]
        },
        {
          name: "Emergency Care",
          description: "Immediate medical attention for urgent health situations and critical care needs.",
          duration: "Variable",
          features: ["24/7 availability", "Urgent care", "Stabilization", "Emergency treatment"]
        }
      ]
    },
    {
      category: "Surgical Services",
      gradient: "from-green-50 to-emerald-50",
      icon: Scissors,
      items: [
        {
          name: "Spaying & Neutering",
          description: "Professional sterilization procedures to prevent unwanted pregnancies and health issues.",
          duration: "1-2 hours",
          features: ["Pre-surgical examination", "Anesthesia monitoring", "Post-operative care", "Pain management"]
        },
        {
          name: "Soft Tissue Surgery",
          description: "Various surgical procedures for treating injuries, masses, and other conditions.",
          duration: "1-3 hours",
          features: ["Tumor removal", "Wound repair", "Abscess treatment", "Foreign body removal"]
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50">
      <Header />

      {/* Hero Section */}
      <section className="relative pt-20 pb-16 px-4 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 via-cyan-500/5 to-slate-100/20"></div>
        <div className="relative max-w-7xl mx-auto text-center">
          <div className="space-y-8">
            <div className="space-y-6">
              <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium">
                <Heart className="w-4 h-4" />
                Professional Veterinary Services
              </div>
              <h1 className="text-5xl lg:text-6xl font-bold text-slate-900 leading-tight">
                Comprehensive Care for
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600"> Your Pet</span>
              </h1>
              <p className="text-xl text-slate-600 leading-relaxed max-w-3xl mx-auto">
                CVETS offers a full range of veterinary services delivered with compassion and expertise.
                From routine check-ups to emergency care, we're here for your pet's every need.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button
                onClick={openAppointmentModal}
                size="lg"
                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                <span>Book Appointment</span>
                <Clock className="w-5 h-5 ml-2" />
              </Button>
              <Button
                asChild
                variant="outline"
                size="lg"
                className="border-2 border-slate-200 bg-white/70 backdrop-blur-sm text-slate-700 hover:bg-slate-50 hover:border-slate-300 px-8 py-4 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
              >
                <a href="tel:0718376311" className="flex items-center space-x-2">
                  <span>Emergency: 0718376311</span>
                </a>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services-list" className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-100/50 via-blue-50/50 to-green-50/50"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-20">
            {services.map((category, categoryIndex) => (
              <div key={categoryIndex} className="space-y-12">
                <div className="text-center space-y-4">
                  <div className={`inline-flex items-center space-x-3 bg-gradient-to-r ${category.gradient} rounded-2xl px-8 py-4`}>
                    <category.icon className="w-8 h-8 text-slate-700" />
                    <h2 className="text-2xl font-bold text-slate-800">{category.category}</h2>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {category.items.map((service, serviceIndex) => (
                    <Card
                      key={serviceIndex}
                      className="group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl"
                    >
                      <CardHeader className="pb-4">
                        <CardTitle className="text-xl font-bold text-slate-800 group-hover:text-blue-600 transition-colors duration-300">
                          {service.name}
                        </CardTitle>
                        <p className="text-slate-600 leading-relaxed">
                          {service.description}
                        </p>
                      </CardHeader>

                      <CardContent className="space-y-6">
                        <div className="flex justify-between items-center">
                          <div className="space-y-1">
                            <p className="text-sm text-slate-500">{service.duration}</p>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <h4 className="font-semibold text-slate-800">Includes:</h4>
                          <ul className="space-y-2">
                            {service.features.map((feature, featureIndex) => (
                              <li key={featureIndex} className="flex items-center space-x-2">
                                <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0" />
                                <span className="text-slate-600 text-sm">{feature}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <Button
                          onClick={openAppointmentModal}
                          className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
                        >
                          Book This Service
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
