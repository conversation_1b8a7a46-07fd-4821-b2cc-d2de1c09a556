import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function smoothScrollToElement(elementId: string, offset: number = 80) {
  const element = document.getElementById(elementId)
  if (element) {
    const elementPosition = element.getBoundingClientRect().top + window.pageYOffset
    const offsetPosition = elementPosition - offset

    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth'
    })
  }
}

export function smoothScrollToAppointmentForm() {
  // If we're not on the contact page, navigate there first
  if (window.location.pathname !== '/contact') {
    window.location.href = '/contact#appointment-form-card'
    return
  }

  // If we're already on the contact page, just scroll
  smoothScrollToElement('appointment-form-card')
}
