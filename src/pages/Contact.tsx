
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { smoothScrollToElement } from '@/lib/utils';
import { createAppointment } from '@/lib/supabase';
import { 
  Phone, 
  Mail, 
  MapPin, 
  Clock, 
  Calendar, 
  Heart, 
  CheckCircle, 
  Star,
  Stethoscope,
  Shield,
  ArrowRight,
  MessageCircle
} from 'lucide-react';

const Contact = () => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    petName: '',
    petType: '',
    service: '',
    preferredDate: '',
    preferredTime: '',
    message: ''
  });

  // Handle smooth scrolling when page loads with hash
  useEffect(() => {
    if (window.location.hash === '#appointment-form-card' || window.location.hash === '#book-appointment' || window.location.hash === '#appointment-form') {
      setTimeout(() => {
        smoothScrollToElement('appointment-form-card');
      }, 100);
    }
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const sendToWhatsApp = () => {
    const whatsappNumber = "254718376311"; // Kenya format
    const message = `*CVETS Appointment Booking*\n\n` +
                   `Name: ${formData.name}\n` +
                   `Email: ${formData.email}\n` +
                   `Phone: ${formData.phone}\n` +
                   `Pet Name: ${formData.petName}\n` +
                   `Pet Type: ${formData.petType}\n` +
                   `Service: ${formData.service}\n` +
                   `Preferred Date: ${formData.preferredDate}\n` +
                   `Preferred Time: ${formData.preferredTime}\n` +
                   `Additional Info: ${formData.message || 'None'}\n\n` +
                   `Please confirm this appointment. Thank you!`;
    
    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  const sendEmail = () => {
    const subject = encodeURIComponent("CVETS Appointment Booking Request");
    const body = encodeURIComponent(`
Dear CVETS Team,

I would like to book an appointment with the following details:

Name: ${formData.name}
Email: ${formData.email}
Phone: ${formData.phone}
Pet Name: ${formData.petName}
Pet Type: ${formData.petType}
Service: ${formData.service}
Preferred Date: ${formData.preferredDate}
Preferred Time: ${formData.preferredTime}
Additional Information: ${formData.message || 'None'}

Please confirm the appointment at your earliest convenience.

Thank you!
    `);
    
    const emailUrl = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
    window.location.href = emailUrl;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.name || !formData.email || !formData.phone || !formData.petName || !formData.petType || !formData.service || !formData.preferredDate || !formData.preferredTime) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Save to Supabase database
      const appointmentData = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        pet_name: formData.petName,
        pet_type: formData.petType,
        service: formData.service,
        preferred_date: formData.preferredDate,
        preferred_time: formData.preferredTime,
        message: formData.message,
        status: 'pending' as const
      };

      await createAppointment(appointmentData);

      console.log('Form submitted and saved to database:', formData);

      // Send to WhatsApp
      sendToWhatsApp();

      // Also send email
      setTimeout(() => {
        sendEmail();
      }, 1000);

      toast({
        title: "Appointment Request Sent!",
        description: "Your appointment request has been saved and sent via WhatsApp and email. We'll confirm shortly.",
      });

      // Reset form after successful submission
      setFormData({
        name: '',
        email: '',
        phone: '',
        petName: '',
        petType: '',
        service: '',
        preferredDate: '',
        preferredTime: '',
        message: ''
      });

    } catch (error) {
      console.error('Error saving appointment:', error);

      // Still send via WhatsApp and email even if database save fails
      sendToWhatsApp();
      setTimeout(() => {
        sendEmail();
      }, 1000);

      toast({
        title: "Appointment Request Sent!",
        description: "Your appointment request has been sent via WhatsApp and email. We'll confirm shortly.",
        variant: "default",
      });
    }
  };

  const contactInfo = [
    {
      icon: Phone,
      title: "Call Us",
      content: "+254 718 376 311",
      description: "Available for appointments & emergencies",
      color: "from-red-100 to-rose-100",
      iconColor: "text-red-600"
    },
    {
      icon: MessageCircle,
      title: "WhatsApp",
      content: "+254 718 376 311",
      description: "Quick responses & booking confirmations",
      color: "from-green-100 to-emerald-100",
      iconColor: "text-green-600"
    },
    {
      icon: Mail,
      title: "Email Us",
      content: "<EMAIL>",
      description: "We'll respond within 24 hours",
      color: "from-blue-100 to-cyan-100",
      iconColor: "text-blue-600"
    },
    {
      icon: Clock,
      title: "Service Hours",
      content: "8 AM - 6 PM",
      description: "Monday through Sunday",
      color: "from-purple-100 to-indigo-100",
      iconColor: "text-purple-600"
    }
  ];

  const services = [
    "Health check-ups",
    "Surgical procedures",
    "Dental care",
    "Diagnostic testing",
    "Emergency care",
    "Preventative care",
    "Parasite control",
    "Local and international travel",
    "Wash & Grooming"
  ];

  const timeSlots = [
    "8:00 AM", "9:00 AM", "10:00 AM", "11:00 AM",
    "12:00 PM", "1:00 PM", "2:00 PM", "3:00 PM",
    "4:00 PM", "5:00 PM", "6:00 PM"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <Header />

      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 via-purple-600/5 to-green-600/5"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center space-y-8">
            <div className="inline-flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-full px-6 py-3 shadow-lg border border-white/50">
              <Heart className="w-5 h-5 text-rose-500" />
              <span className="text-sm font-medium text-slate-700">Book Your Appointment</span>
            </div>
            
            <h1 className="text-5xl md:text-6xl font-bold leading-tight">
              <span className="bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 bg-clip-text text-transparent">
                Schedule Your Pet's
              </span>
              <br />
              <span className="bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
                Veterinary Care Today
              </span>
            </h1>
            
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
              Professional veterinary services for your beloved pets. 
              Book online and we'll confirm your appointment via WhatsApp and email within 2 hours.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Info Cards */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {contactInfo.map((info, index) => (
              <Card 
                key={index} 
                className="group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl"
              >
                <CardContent className="p-8 text-center space-y-4">
                  <div className={`w-16 h-16 bg-gradient-to-r ${info.color} rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                    <info.icon className={`w-8 h-8 ${info.iconColor}`} />
                  </div>
                  
                  <div className="space-y-2">
                    <h3 className="text-lg font-bold text-slate-800">{info.title}</h3>
                    <p className="text-xl font-semibold bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
                      {info.content}
                    </p>
                    <p className="text-sm text-slate-600">{info.description}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Booking Form */}
      <section id="appointment-form" className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/30 via-white/30 to-green-50/30"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start">
            {/* Form */}
            <div className="space-y-8">
              <div id="book-appointment" className="text-center lg:text-left space-y-4">
                <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-green-100 to-emerald-100 rounded-2xl px-6 py-3">
                  <Calendar className="w-5 h-5 text-green-600" />
                  <span className="text-sm font-medium text-slate-700">Online Booking</span>
                </div>

                <h2 className="text-4xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 bg-clip-text text-transparent">
                  Book Your Appointment
                </h2>
                
                <p className="text-lg text-slate-600">
                  Fill out the form below and we'll contact you via WhatsApp and email to confirm your appointment.
                </p>
              </div>

              <Card id="appointment-form-card" className="bg-white/80 backdrop-blur-sm border-0 shadow-2xl rounded-3xl overflow-hidden">
                <CardHeader className="bg-gradient-to-r from-blue-50 to-green-50 pb-6">
                  <CardTitle className="text-2xl text-slate-800 text-center">Appointment Details</CardTitle>
                </CardHeader>
                
                <CardContent className="p-8">
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="name" className="text-slate-700 font-medium">Your Name *</Label>
                        <Input
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          className="bg-white/70 border-slate-200 rounded-xl focus:ring-blue-500"
                          required
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="email" className="text-slate-700 font-medium">Email Address *</Label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          className="bg-white/70 border-slate-200 rounded-xl focus:ring-blue-500"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="phone" className="text-slate-700 font-medium">Phone Number *</Label>
                        <Input
                          id="phone"
                          name="phone"
                          type="tel"
                          value={formData.phone}
                          onChange={handleInputChange}
                          className="bg-white/70 border-slate-200 rounded-xl focus:ring-blue-500"
                          required
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="petName" className="text-slate-700 font-medium">Pet's Name *</Label>
                        <Input
                          id="petName"
                          name="petName"
                          value={formData.petName}
                          onChange={handleInputChange}
                          className="bg-white/70 border-slate-200 rounded-xl focus:ring-blue-500"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="petType" className="text-slate-700 font-medium">Pet Type *</Label>
                        <select
                          id="petType"
                          name="petType"
                          value={formData.petType}
                          onChange={handleInputChange}
                          className="w-full h-10 px-3 bg-white/70 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          required
                        >
                          <option value="">Select pet type</option>
                          <option value="dog">Dog</option>
                          <option value="cat">Cat</option>
                          <option value="bird">Bird</option>
                          <option value="rabbit">Rabbit</option>
                          <option value="other">Other</option>
                        </select>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="service" className="text-slate-700 font-medium">Service Needed *</Label>
                        <select
                          id="service"
                          name="service"
                          value={formData.service}
                          onChange={handleInputChange}
                          className="w-full h-10 px-3 bg-white/70 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          required
                        >
                          <option value="">Select service</option>
                          {services.map((service, index) => (
                            <option key={index} value={service}>{service}</option>
                          ))}
                        </select>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="preferredDate" className="text-slate-700 font-medium">Preferred Date *</Label>
                        <Input
                          id="preferredDate"
                          name="preferredDate"
                          type="date"
                          value={formData.preferredDate}
                          onChange={handleInputChange}
                          className="bg-white/70 border-slate-200 rounded-xl focus:ring-blue-500"
                          required
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="preferredTime" className="text-slate-700 font-medium">Preferred Time *</Label>
                        <select
                          id="preferredTime"
                          name="preferredTime"
                          value={formData.preferredTime}
                          onChange={handleInputChange}
                          className="w-full h-10 px-3 bg-white/70 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          required
                        >
                          <option value="">Select time</option>
                          {timeSlots.map((time, index) => (
                            <option key={index} value={time}>{time}</option>
                          ))}
                        </select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="message" className="text-slate-700 font-medium">Additional Information</Label>
                      <textarea
                        id="message"
                        name="message"
                        value={formData.message}
                        onChange={handleInputChange}
                        rows={4}
                        className="w-full px-3 py-2 bg-white/70 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                        placeholder="Please describe any symptoms, concerns, or special requirements..."
                      />
                    </div>

                    <Button 
                      type="submit"
                      size="lg"
                      className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
                    >
                      <MessageCircle className="w-5 h-5 mr-2" />
                      Send via WhatsApp & Email
                      <ArrowRight className="w-5 h-5 ml-2" />
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>

            {/* Why Choose Us */}
            <div className="space-y-8">
              <div id="book-appointment" className="text-center lg:text-left space-y-4">
                <h2 className="text-4xl font-bold bg-gradient-to-r from-slate-900 via-green-900 to-slate-900 bg-clip-text text-transparent">
                  Why Choose CVETS?
                </h2>
              </div>

              <div className="space-y-6">
                {[
                  {
                    icon: Shield,
                    title: "Professional Care",
                    description: "Experienced veterinarians providing comprehensive medical care for all types of pets.",
                    color: "from-blue-100 to-cyan-100"
                  },
                  {
                    icon: Stethoscope,
                    title: "Modern Equipment",
                    description: "State-of-the-art diagnostic and surgical equipment for accurate diagnosis and treatment.",
                    color: "from-green-100 to-emerald-100"
                  },
                  {
                    icon: Heart,
                    title: "Compassionate Service",
                    description: "We treat every pet with love and care, ensuring their comfort throughout their visit.",
                    color: "from-rose-100 to-pink-100"
                  },
                  {
                    icon: Clock,
                    title: "Convenient Hours",
                    description: "Open 7 days a week with emergency services available when you need us most.",
                    color: "from-purple-100 to-indigo-100"
                  }
                ].map((feature, index) => (
                  <Card key={index} className="bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 rounded-2xl">
                    <CardContent className="p-6">
                      <div className="flex items-start space-x-4">
                        <div className={`w-12 h-12 bg-gradient-to-r ${feature.color} rounded-xl flex items-center justify-center flex-shrink-0`}>
                          <feature.icon className="w-6 h-6 text-slate-700" />
                        </div>
                        <div className="space-y-2">
                          <h3 className="text-lg font-bold text-slate-800">{feature.title}</h3>
                          <p className="text-slate-600 leading-relaxed">{feature.description}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border-0 shadow-lg rounded-2xl">
                <CardContent className="p-8 text-center space-y-4">
                  <div className="w-16 h-16 bg-white/80 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto">
                    <MessageCircle className="w-8 h-8 text-green-500" />
                  </div>
                  
                  <h3 className="text-xl font-bold text-slate-800">Quick WhatsApp Booking</h3>
                  <p className="text-slate-600 leading-relaxed">
                    Get instant confirmation and updates through WhatsApp for the fastest booking experience.
                  </p>
                  
                  <Button 
                    asChild
                    variant="outline"
                    className="border-2 border-green-200 bg-white/70 text-green-600 hover:bg-green-50 hover:border-green-300 rounded-xl"
                  >
                    <a href="https://wa.me/254718376311" target="_blank" rel="noopener noreferrer" className="flex items-center space-x-2">
                      <MessageCircle className="w-4 h-4" />
                      <span>WhatsApp: +254 718 376 311</span>
                    </a>
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Contact;
