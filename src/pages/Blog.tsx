
import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { Link } from 'react-router-dom';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { getBlogPosts, type BlogPost } from '@/lib/supabase';
import { Calendar, Clock, User, Search, Heart, Shield, Stethoscope, Star, ArrowRight, Settings, Loader2 } from 'lucide-react';

const Blog = () => {
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    loadBlogPosts();
  }, []);

  const loadBlogPosts = async () => {
    try {
      setLoading(true);
      const data = await getBlogPosts(true); // Only published posts
      setBlogPosts(data || []);
    } catch (error) {
      console.error('Error loading blog posts:', error);
      toast({
        title: "Error",
        description: "Failed to load blog posts. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const filteredPosts = blogPosts.filter(post =>
    post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (post.excerpt && post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const featuredPost = filteredPosts.length > 0 ? filteredPosts[0] : null;
  const regularPosts = filteredPosts.slice(1);




  const categories = [
    { name: "All Posts", count: 12, color: "bg-gradient-to-r from-slate-500 to-slate-600" },
    { name: "Health & Wellness", count: 8, color: "bg-gradient-to-r from-green-500 to-emerald-600" },
    { name: "Vaccinations", count: 3, color: "bg-gradient-to-r from-blue-500 to-cyan-600" },
    { name: "Emergency Care", count: 2, color: "bg-gradient-to-r from-red-500 to-rose-600" },
    { name: "Mobile Care", count: 4, color: "bg-gradient-to-r from-purple-500 to-indigo-600" },
    { name: "Nutrition", count: 3, color: "bg-gradient-to-r from-orange-500 to-amber-600" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <Header />

      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 via-purple-600/5 to-green-600/5"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center space-y-8">
            <div className="inline-flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-full px-6 py-3 shadow-lg border border-white/50">
              <Heart className="w-5 h-5 text-rose-500" />
              <span className="text-sm font-medium text-slate-700">Pet Care Insights</span>
            </div>
            
            <h1 className="text-5xl md:text-6xl font-bold leading-tight">
              <span className="bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 bg-clip-text text-transparent">
                Expert Pet Care
              </span>
              <br />
              <span className="bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
                Knowledge & Tips
              </span>
            </h1>
            
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
              Stay informed with the latest veterinary insights, health tips, and care guidance 
              to keep your beloved pets happy, healthy, and thriving.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                <Input
                  placeholder="Search articles..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-white/80 backdrop-blur-sm border-slate-200 rounded-2xl shadow-lg"
                />
              </div>
              {searchTerm && (
                <Button
                  onClick={() => setSearchTerm('')}
                  variant="outline"
                  className="bg-white/80 backdrop-blur-sm border-slate-200 rounded-2xl shadow-lg"
                >
                  Clear
                </Button>
              )}
            </div>

            <div className="pt-4 flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/add-blog">
                <Button variant="outline" className="border-2 border-blue-200 bg-white/70 text-blue-700 hover:bg-blue-50 rounded-2xl shadow-lg">
                  + Add New Article
                </Button>
              </Link>
              <Link to="/manage-blogs">
                <Button variant="outline" className="border-2 border-purple-200 bg-white/70 text-purple-700 hover:bg-purple-50 rounded-2xl shadow-lg">
                  <Settings className="w-4 h-4 mr-2" />
                  Manage Blogs
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap gap-4 justify-center">
            {categories.map((category, index) => (
              <Button
                key={index}
                variant="outline"
                className={`${category.color} text-white border-0 hover:opacity-90 transform hover:scale-105 transition-all duration-300 rounded-2xl shadow-lg`}
              >
                {category.name} ({category.count})
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Post */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {loading ? (
            <div className="flex justify-center items-center py-20">
              <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
              <span className="ml-2 text-slate-600">Loading articles...</span>
            </div>
          ) : featuredPost ? (
            <>
              <div className="text-center mb-12">
                <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-yellow-100 to-orange-100 rounded-2xl px-8 py-4 mb-6">
                  <Star className="w-6 h-6 text-orange-600" />
                  <span className="text-lg font-semibold text-slate-700">Featured Article</span>
                </div>
              </div>
          
          <Card className="group relative overflow-hidden bg-white/80 backdrop-blur-sm border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 ease-out hover:-translate-y-1 rounded-3xl max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2">
              <div className="relative h-64 lg:h-full overflow-hidden rounded-t-3xl lg:rounded-l-3xl lg:rounded-tr-none">
                <img
                  src={featuredPost.image_url || 'https://images.unsplash.com/photo-1582562124811-c09040d0a901?auto=format&fit=crop&w=800&h=400'}
                  alt={featuredPost.title}
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                <Badge className="absolute top-4 left-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-white border-0 shadow-lg">
                  Featured
                </Badge>
              </div>
              
              <CardContent className="p-12 space-y-6 flex flex-col justify-center">
                <Badge className="w-fit bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 border-green-200">
                  Featured
                </Badge>

                <CardTitle className="text-3xl font-bold text-slate-800 group-hover:text-blue-600 transition-colors duration-300 leading-tight">
                  {featuredPost.title}
                </CardTitle>

                <p className="text-slate-600 leading-relaxed text-lg">
                  {featuredPost.excerpt || featuredPost.content.substring(0, 200) + '...'}
                </p>

                <div className="flex items-center space-x-6 text-sm text-slate-500">
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4" />
                    <span>{featuredPost.author}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4" />
                    <span>{new Date(featuredPost.created_at || '').toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4" />
                    <span>{Math.ceil(featuredPost.content.length / 200)} min read</span>
                  </div>
                </div>
                
                <Link to={`/blog/${featuredPost.id}`}>
                  <Button className="w-fit bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-2xl shadow-lg group">
                    Read Full Article
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                  </Button>
                </Link>
              </CardContent>
            </div>
          </Card>
            </>
          ) : (
            <div className="text-center py-20">
              <Heart className="w-16 h-16 text-slate-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-slate-600 mb-2">No articles found</h3>
              <p className="text-slate-500">Check back soon for new content!</p>
            </div>
          )}
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 space-y-6">
            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 bg-clip-text text-transparent">
              Latest Articles
            </h2>
            <p className="text-xl text-slate-600">
              Expert insights and practical tips for pet care
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {regularPosts.map((post, index) => (
              <Card
                key={post.id}
                className="group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl"
              >
                <div className="relative h-48 overflow-hidden rounded-t-3xl">
                  <img
                    src={post.image_url || 'https://images.unsplash.com/photo-1582562124811-c09040d0a901?auto=format&fit=crop&w=600&h=300'}
                    alt={post.title}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  <Badge className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm text-slate-700 border-0 shadow-lg">
                    {post.author}
                  </Badge>
                </div>
                
                <CardHeader className="pb-3">
                  <CardTitle className="text-xl font-bold text-slate-800 group-hover:text-blue-600 transition-colors duration-300 leading-tight">
                    {post.title}
                  </CardTitle>
                </CardHeader>
                
                <CardContent className="pt-0 space-y-4">
                  <p className="text-slate-600 leading-relaxed">
                    {post.excerpt || post.content.substring(0, 150) + '...'}
                  </p>

                  <div className="flex items-center justify-between text-sm text-slate-500">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-3 h-3" />
                        <span>{new Date(post.created_at || '').toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="w-3 h-3" />
                        <span>{Math.ceil(post.content.length / 200)} min read</span>
                      </div>
                    </div>
                  </div>

                  <Link to={`/blog/${post.id}`}>
                    <Button 
                      variant="outline" 
                      className="w-full border-slate-200 bg-white/70 text-slate-700 hover:bg-blue-50 hover:text-blue-700 hover:border-blue-200 transition-all duration-300 rounded-2xl group"
                    >
                      Read More
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-16">
            <Button 
              size="lg" 
              variant="outline"
              className="border-2 border-slate-200 bg-white/70 backdrop-blur-sm text-slate-700 hover:bg-slate-50 hover:border-slate-300 px-8 py-4 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
            >
              Load More Articles
            </Button>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-700"></div>
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto space-y-8">
            <div className="space-y-4">
              <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto">
                <Heart className="w-8 h-8 text-white" />
              </div>
              
              <h2 className="text-4xl md:text-5xl font-bold text-white leading-tight">
                Stay Updated with
                <br />
                <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                  Pet Care Tips
                </span>
              </h2>
            </div>
            
            <p className="text-xl text-blue-100 leading-relaxed">
              Subscribe to our newsletter for the latest veterinary insights, 
              health tips, and exclusive content delivered to your inbox.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
              <Input 
                placeholder="Enter your email" 
                className="flex-1 bg-white/10 backdrop-blur-sm border-white/20 text-white placeholder-white/70 rounded-2xl"
              />
              <Button 
                size="lg" 
                className="bg-white text-blue-600 hover:bg-blue-50 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                Subscribe
              </Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Blog;
