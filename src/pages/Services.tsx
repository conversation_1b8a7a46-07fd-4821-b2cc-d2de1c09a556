import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Link } from 'react-router-dom';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Stethoscope, Heart, Shield, Clock, MapPin, CreditCard, CheckCircle, Scissors, Plane, Bug } from 'lucide-react';

const Services = () => {
  const services = [
    {
      category: "Medical Care",
      gradient: "from-blue-50 to-cyan-50",
      icon: Stethoscope,
      items: [
        {
          name: "Vaccinations",
          description: "Complete vaccination programs including core and non-core vaccines to protect your pet's health.",
          duration: "30 minutes",
          popular: true,
          image: "https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?auto=format&fit=crop&w=400&h=250"
        },
        {
          name: "Health Check-ups",
          description: "Comprehensive physical examinations to monitor your pet's overall health and detect early signs of illness.",
          duration: "45 minutes",
          popular: true,
          image: "https://images.unsplash.com/photo-1582562124811-c09040d0a901?auto=format&fit=crop&w=400&h=250"
        },
        {
          name: "Diagnostic Testing",
          description: "Advanced diagnostic services including blood work, X-rays, and ultrasounds for accurate diagnosis.",
          duration: "60 minutes",
          image: "https://images.unsplash.com/photo-1452378174528-3090a4bba7b2?auto=format&fit=crop&w=400&h=250"
        }
      ]
    },
    {
      category: "Surgical Services",
      gradient: "from-green-50 to-emerald-50",
      icon: Shield,
      items: [
        {
          name: "Surgical Procedures",
          description: "Expert surgical care including spaying, neutering, and other essential surgical interventions.",
          duration: "2-4 hours",
          image: "https://images.unsplash.com/photo-1582562124811-c09040d0a901?auto=format&fit=crop&w=400&h=250"
        },
        {
          name: "Dental Care",
          description: "Professional dental cleaning, extractions, and oral health maintenance for optimal pet health.",
          duration: "90 minutes",
          image: "https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?auto=format&fit=crop&w=400&h=250"
        }
      ]
    },
    {
      category: "Emergency & Critical Care",
      gradient: "from-red-50 to-rose-50",
      icon: Heart,
      items: [
        {
          name: "Emergency Care",
          description: "24/7 emergency veterinary services for urgent medical situations requiring immediate attention.",
          duration: "Varies",
          emergency: true,
          image: "https://images.unsplash.com/photo-1452378174528-3090a4bba7b2?auto=format&fit=crop&w=400&h=250"
        }
      ]
    },
    {
      category: "Preventative & Wellness",
      gradient: "from-purple-50 to-indigo-50",
      icon: Bug,
      items: [
        {
          name: "Preventative Care",
          description: "Comprehensive preventative medicine including routine screenings and wellness programs.",
          duration: "30 minutes",
          image: "https://images.unsplash.com/photo-1582562124811-c09040d0a901?auto=format&fit=crop&w=400&h=250"
        },
        {
          name: "Parasite Control",
          description: "Complete parasite prevention and treatment including flea, tick, and worm control programs.",
          duration: "20 minutes",
          image: "https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?auto=format&fit=crop&w=400&h=250"
        }
      ]
    },
    {
      category: "Specialized Services",
      gradient: "from-orange-50 to-amber-50",
      icon: Plane,
      items: [
        {
          name: "Local and International Travel",
          description: "Travel health certificates, vaccinations, and documentation for local and international pet travel.",
          duration: "45 minutes",
          image: "https://images.unsplash.com/photo-1452378174528-3090a4bba7b2?auto=format&fit=crop&w=400&h=250"
        },
        {
          name: "Wash & Grooming",
          description: "Professional pet grooming services including bathing, nail trimming, and coat maintenance.",
          duration: "60 minutes",
          image: "https://images.unsplash.com/photo-1582562124811-c09040d0a901?auto=format&fit=crop&w=400&h=250"
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <Header />

      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 via-purple-600/5 to-green-600/5"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center transform transition-all duration-1000 ease-out animate-fade-in">
            <div className="inline-flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-full px-6 py-3 mb-8 shadow-lg border border-white/50">
              <Stethoscope className="w-5 h-5 text-blue-600" />
              <span className="text-sm font-medium text-slate-700">Professional Veterinary Care</span>
            </div>
            <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 bg-clip-text text-transparent mb-8 leading-tight">
              CVETS Veterinary
              <br />
              <span className="bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
                Services
              </span>
            </h1>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto mb-12 leading-relaxed">
              Comprehensive veterinary care for your beloved pets. 
              From routine check-ups to emergency care, we're here for all your pet's health needs.
            </p>
            <Button 
              asChild 
              size="lg" 
              className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
            >
              <Link to="/contact">Schedule Appointment</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Services List */}
      <section className="py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {services.map((category, categoryIndex) => (
            <div key={categoryIndex} className="mb-32">
              <div className="text-center mb-20">
                <div className={`inline-flex items-center space-x-3 bg-gradient-to-r ${category.gradient} rounded-2xl px-8 py-4 mb-8`}>
                  <category.icon className="w-6 h-6 text-slate-700" />
                  <h2 className="text-3xl font-bold text-slate-800">{category.category}</h2>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {category.items.map((service, serviceIndex) => (
                  <Card 
                    key={serviceIndex} 
                    className="group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl"
                  >
                    {service.popular && (
                      <Badge className="absolute top-4 right-4 z-10 bg-gradient-to-r from-green-500 to-emerald-500 text-white border-0 shadow-lg">
                        Popular
                      </Badge>
                    )}
                    {service.emergency && (
                      <Badge className="absolute top-4 right-4 z-10 bg-gradient-to-r from-red-500 to-rose-500 text-white border-0 shadow-lg">
                        Emergency
                      </Badge>
                    )}
                    
                    {service.image && (
                      <div className="relative h-48 overflow-hidden rounded-t-3xl">
                        <img 
                          src={service.image} 
                          alt={service.name}
                          className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                      </div>
                    )}
                    
                    <CardHeader className="pb-4">
                      <CardTitle className="text-xl text-slate-800 group-hover:text-blue-600 transition-colors duration-300">
                        {service.name}
                      </CardTitle>
                    </CardHeader>
                    
                    <CardContent className="pt-0">
                      <p className="text-slate-600 mb-6 leading-relaxed">{service.description}</p>
                      
                      <div className="flex justify-between items-center mb-6">
                        <div>
                          <p className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
                            Call for pricing
                          </p>
                          <p className="text-sm text-slate-500 flex items-center space-x-1">
                            <Clock className="w-3 h-3" />
                            <span>{service.duration}</span>
                          </p>
                        </div>
                      </div>
                      
                      <Button 
                        asChild 
                        className="w-full bg-gradient-to-r from-slate-100 to-slate-200 text-slate-700 border border-slate-200 hover:from-blue-50 hover:to-blue-100 hover:text-blue-700 hover:border-blue-200 transition-all duration-300 rounded-2xl"
                        variant="outline"
                      >
                        <Link to="/contact">Book This Service</Link>
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Products Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-green-50/50 via-blue-50/50 to-purple-50/50"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-full px-6 py-3 mb-8 shadow-lg border border-white/50">
              <Heart className="w-5 h-5 text-rose-500" />
              <span className="text-sm font-medium text-slate-700">Pet Products</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 bg-clip-text text-transparent mb-6">
              Quality Pet Products
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
              Premium pet care products, medications, and accessories available at our clinic.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                name: "Pet Medications",
                description: "Prescription and over-the-counter medications for various pet health conditions.",
                image: "https://images.unsplash.com/photo-1582562124811-c09040d0a901?auto=format&fit=crop&w=400&h=250",
                category: "Healthcare"
              },
              {
                name: "Pet Food & Nutrition",
                description: "High-quality pet food and nutritional supplements for optimal pet health.",
                image: "https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?auto=format&fit=crop&w=400&h=250",
                category: "Nutrition"
              },
              {
                name: "Pet Accessories",
                description: "Collars, leashes, toys, and other essential accessories for your pet's comfort.",
                image: "https://images.unsplash.com/photo-1452378174528-3090a4bba7b2?auto=format&fit=crop&w=400&h=250",
                category: "Accessories"
              }
            ].map((product, index) => (
              <Card key={index} className="group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl">
                <div className="relative h-48 overflow-hidden rounded-t-3xl">
                  <img 
                    src={product.image} 
                    alt={product.name}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  <Badge className="absolute top-4 left-4 bg-gradient-to-r from-purple-500 to-indigo-500 text-white border-0 shadow-lg">
                    {product.category}
                  </Badge>
                </div>
                
                <CardHeader className="pb-4">
                  <CardTitle className="text-xl text-slate-800 group-hover:text-blue-600 transition-colors duration-300">
                    {product.name}
                  </CardTitle>
                </CardHeader>
                
                <CardContent className="pt-0">
                  <p className="text-slate-600 mb-6 leading-relaxed">{product.description}</p>
                  
                  <Button 
                    asChild 
                    className="w-full bg-gradient-to-r from-purple-100 to-indigo-100 text-purple-700 border border-purple-200 hover:from-purple-50 hover:to-indigo-50 hover:text-purple-800 hover:border-purple-300 transition-all duration-300 rounded-2xl"
                    variant="outline"
                  >
                    <Link to="/contact">Inquire About Products</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Additional Information */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/50 via-purple-50/50 to-green-50/50"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl rounded-3xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-1">
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-100 to-blue-200 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <MapPin className="w-8 h-8 text-blue-600" />
                </div>
                <CardTitle className="text-slate-800">Service Location</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-slate-600 mb-6 leading-relaxed">
                  Located in Kenya, serving pets across the region with:
                </p>
                <div className="space-y-3">
                  {['In-clinic consultations', 'Mobile veterinary services', 'Emergency house calls', 'Travel health services'].map((service, index) => (
                    <div key={index} className="flex items-center justify-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm text-slate-600">{service}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl rounded-3xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-1">
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 bg-gradient-to-r from-green-100 to-green-200 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
                <CardTitle className="text-slate-800">What's Included</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <div className="space-y-3">
                  {[
                    'Professional consultation',
                    'Detailed health assessment', 
                    'Treatment recommendations',
                    'Follow-up care instructions',
                    'Health records provided'
                  ].map((item, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-green-400 to-green-500 rounded-full"></div>
                      <span className="text-sm text-slate-600">{item}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl rounded-3xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-1">
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-100 to-purple-200 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <CreditCard className="w-8 h-8 text-purple-600" />
                </div>
                <CardTitle className="text-slate-800">Payment Options</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-slate-600 mb-6 leading-relaxed">
                  We accept various payment methods:
                </p>
                <div className="space-y-3">
                  {['Cash payments', 'Mobile money (M-Pesa)', 'Bank transfers', 'Credit/Debit cards', 'Insurance claims'].map((option, index) => (
                    <div key={index} className="flex items-center justify-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-purple-500" />
                      <span className="text-sm text-slate-600">{option}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-700"></div>
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
              Ready to Book Your Pet's 
              <br />
              <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                Appointment?
              </span>
            </h2>
            <p className="text-xl text-blue-100 mb-12 leading-relaxed">
              Contact CVETS today to schedule professional veterinary care for your beloved pet
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button 
                asChild 
                size="lg" 
                className="bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                <Link to="/contact">Book Online</Link>
              </Button>
              <Button 
                asChild 
                size="lg" 
                variant="outline" 
                className="text-white border-white/50 bg-white/10 backdrop-blur-sm hover:bg-white hover:text-blue-600 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                <a href="tel:+254718376311">Call +254 718 376 311</a>
              </Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Services;
