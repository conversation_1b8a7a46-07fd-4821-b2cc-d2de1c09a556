
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Link } from 'react-router-dom';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { GraduationCap, Award, Heart, Users, Calendar, MapPin, Phone, Star, Shield, Stethoscope } from 'lucide-react';

const About = () => {
  const qualifications = [
    {
      title: "Doctor of Veterinary Medicine",
      institution: "University of Veterinary Sciences",
      year: "2012",
      icon: GraduationCap,
      color: "from-blue-100 to-cyan-100"
    },
    {
      title: "Mobile Veterinary Certification",
      institution: "American Mobile Veterinary Association",
      year: "2014",
      icon: Award,
      color: "from-green-100 to-emerald-100"
    },
    {
      title: "Emergency Care Specialist",
      institution: "Veterinary Emergency Board",
      year: "2016",
      icon: Heart,
      color: "from-red-100 to-rose-100"
    }
  ];

  const achievements = [
    {
      number: "2,500+",
      label: "Happy Pets Treated",
      icon: Heart,
      color: "text-rose-600"
    },
    {
      number: "10+",
      label: "Years Experience",
      icon: Calendar,
      color: "text-blue-600"
    },
    {
      number: "500+",
      label: "Families Served",
      icon: Users,
      color: "text-green-600"
    },
    {
      number: "25",
      label: "Mile Service Radius",
      icon: MapPin,
      color: "text-purple-600"
    }
  ];

  const values = [
    {
      title: "Compassionate Care",
      description: "Every pet deserves gentle, loving treatment in a stress-free environment.",
      icon: Heart,
      gradient: "from-rose-50 to-pink-50"
    },
    {
      title: "Professional Excellence",
      description: "Maintaining the highest standards of veterinary medicine and client service.",
      icon: Shield,
      gradient: "from-blue-50 to-cyan-50"
    },
    {
      title: "Convenient Access",
      description: "Bringing quality veterinary care directly to your home when you need it most.",
      icon: Stethoscope,
      gradient: "from-green-50 to-emerald-50"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <Header />

      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 via-purple-600/5 to-green-600/5"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8">
              <div className="inline-flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-full px-6 py-3 shadow-lg border border-white/50">
                <Heart className="w-5 h-5 text-rose-500" />
                <span className="text-sm font-medium text-slate-700">About Dr. Cynthia</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold leading-tight">
                <span className="bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 bg-clip-text text-transparent">
                  Dedicated to Your Pet's
                </span>
                <br />
                <span className="bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
                  Health & Happiness
                </span>
              </h1>
              
              <p className="text-xl text-slate-600 leading-relaxed">
                With over a decade of experience in veterinary medicine, Dr. Cynthia brings
                professional, compassionate care directly to your home, ensuring your pets receive
                the best treatment in their most comfortable environment.
              </p>
              
              <Button 
                asChild 
                size="lg" 
                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                <Link to="/contact">Schedule Consultation</Link>
              </Button>
            </div>
            
            <div className="relative">
              <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl p-8 transform -rotate-2 hover:rotate-0 transition-all duration-700 ease-out">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-green-50/50 rounded-3xl"></div>
                <div className="relative space-y-6">
                  <div className="relative mx-auto w-48 h-48">
                    <img
                      src="https://images.unsplash.com/photo-1582562124811-c09040d0a901?auto=format&fit=crop&w=400&h=400"
                      alt="Dr. Cynthia"
                      className="w-full h-full rounded-3xl object-cover shadow-lg"
                    />
                    <div className="absolute -bottom-4 -right-4 bg-gradient-to-r from-green-400 to-emerald-500 rounded-2xl p-4 shadow-lg">
                      <Stethoscope className="w-8 h-8 text-white" />
                    </div>
                  </div>
                  
                  <div className="text-center space-y-2">
                    <h3 className="text-2xl font-bold text-slate-800">Dr. Cynthia, DVM</h3>
                    <p className="text-blue-600 font-medium">Mobile Veterinarian</p>
                    <div className="flex justify-center space-x-1">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/30 via-white/30 to-green-50/30"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {achievements.map((achievement, index) => (
              <Card 
                key={index} 
                className="text-center bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 rounded-3xl hover:-translate-y-1"
              >
                <CardContent className="p-8 space-y-4">
                  <achievement.icon className={`w-12 h-12 mx-auto ${achievement.color}`} />
                  <div className="space-y-2">
                    <p className="text-3xl font-bold text-slate-800">{achievement.number}</p>
                    <p className="text-slate-600 font-medium">{achievement.label}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-100/50 via-blue-50/50 to-green-50/50"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16 space-y-6">
              <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 bg-clip-text text-transparent">
                My Journey to
                <br />
                <span className="bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
                  Mobile Veterinary Care
                </span>
              </h2>
            </div>
            
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-2xl rounded-3xl overflow-hidden">
              <div className="grid grid-cols-1 lg:grid-cols-2">
                <div className="relative h-64 lg:h-full">
                  <img 
                    src="https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?auto=format&fit=crop&w=600&h=400" 
                    alt="Veterinarian with pets"
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-green-600/20"></div>
                </div>
                
                <CardContent className="p-12 space-y-6">
                  <h3 className="text-2xl font-bold text-slate-800">A Passion Born from Love</h3>
                  
                  <div className="space-y-4 text-slate-600 leading-relaxed">
                    <p>
                      My journey into veterinary medicine began with my childhood companion, Max, 
                      a golden retriever who taught me the profound bond between humans and animals. 
                      Watching him struggle with anxiety during clinic visits inspired my dedication 
                      to mobile veterinary care.
                    </p>
                    
                    <p>
                      After graduating from the University of Veterinary Sciences, I spent years 
                      in traditional practice before realizing that pets heal better in familiar 
                      environments. This revelation led me to specialize in mobile care, bringing 
                      professional treatment directly to loving homes.
                    </p>
                    
                    <p>
                      Today, I'm proud to serve our community with compassionate, convenient care 
                      that reduces stress for both pets and their families. Every house call 
                      reinforces my belief that the best medicine comes with love, comfort, and 
                      the familiar presence of home.
                    </p>
                  </div>
                  
                  <div className="flex items-center space-x-4 pt-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-100 to-blue-200 rounded-2xl flex items-center justify-center">
                      <Heart className="w-6 h-6 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-semibold text-slate-800">Dr. Cynthia</p>
                      <p className="text-sm text-slate-600">Founder & Mobile Veterinarian</p>
                    </div>
                  </div>
                </CardContent>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Qualifications Section */}
      <section className="py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20 space-y-6">
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-purple-100 to-indigo-100 rounded-2xl px-8 py-4">
              <GraduationCap className="w-6 h-6 text-purple-600" />
              <span className="text-lg font-semibold text-slate-700">Education & Certifications</span>
            </div>
            
            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-900 via-purple-900 to-slate-900 bg-clip-text text-transparent">
              Professional Qualifications
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {qualifications.map((qual, index) => (
              <Card 
                key={index} 
                className="group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl"
              >
                <CardHeader className="text-center space-y-4">
                  <div className={`w-20 h-20 bg-gradient-to-r ${qual.color} rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                    <qual.icon className="w-10 h-10 text-slate-700" />
                  </div>
                  
                  <div className="space-y-2">
                    <Badge className="bg-gradient-to-r from-blue-500 to-green-500 text-white border-0">
                      {qual.year}
                    </Badge>
                    <CardTitle className="text-xl text-slate-800 group-hover:text-blue-600 transition-colors duration-300">
                      {qual.title}
                    </CardTitle>
                  </div>
                </CardHeader>
                
                <CardContent className="text-center pb-8">
                  <p className="text-slate-600 font-medium">{qual.institution}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/30 via-white/30 to-green-50/30"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20 space-y-6">
            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 bg-clip-text text-transparent">
              Our Core Values
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <Card 
                key={index} 
                className={`group relative overflow-hidden bg-gradient-to-br ${value.gradient} border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl`}
              >
                <CardContent className="p-8 text-center space-y-6">
                  <div className="w-16 h-16 bg-white/80 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <value.icon className="w-8 h-8 text-slate-700" />
                  </div>
                  
                  <h3 className="text-xl font-bold text-slate-800">{value.title}</h3>
                  <p className="text-slate-600 leading-relaxed">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-700"></div>
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto space-y-8">
            <h2 className="text-4xl md:text-5xl font-bold text-white leading-tight">
              Ready to Experience
              <br />
              <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                Compassionate Care?
              </span>
            </h2>
            
            <p className="text-xl text-blue-100 leading-relaxed">
              Schedule a consultation and discover why families trust Dr. Cynthia for their pets' health and wellbeing
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button 
                asChild 
                size="lg" 
                className="bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                <Link to="/contact">Book Consultation</Link>
              </Button>
              <Button 
                asChild 
                size="lg" 
                variant="outline" 
                className="text-white border-white/50 bg-white/10 backdrop-blur-sm hover:bg-white hover:text-blue-600 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                <a href="tel:+254718376311">Call +254 718 376 311</a>
              </Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default About;
