
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Phone, Clock, Home, Calendar, Heart, Shield, Star, ArrowRight, Stethoscope, Scissors, Zap, Activity, Siren, <PERSON>brella, Bug, Plane, Sparkles } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { smoothScrollToAppointmentForm } from '@/lib/utils';

const Index = () => {
  const services = [
    {
      title: "Health Check-ups",
      description: "Comprehensive health examinations to ensure your pet's optimal wellbeing and early detection of health issues.",
      price: "Starting at KSh 2,500",
      image: "https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?auto=format&fit=crop&w=400&h=300",
      icon: Stethoscope
    },
    {
      title: "Surgical Procedures",
      description: "Professional surgical services including spaying, neutering, and other necessary procedures.",
      price: "Starting at KSh 8,000",
      image: "https://images.unsplash.com/photo-1582562124811-c09040d0a901?auto=format&fit=crop&w=400&h=300",
      icon: Scissors
    },
    {
      title: "Dental Care",
      description: "Complete dental health services including cleaning, extractions, and oral health maintenance.",
      price: "Starting at KSh 3,500",
      image: "https://images.unsplash.com/photo-1601758228041-f3b2795255f1?auto=format&fit=crop&w=400&h=300",
      icon: Sparkles
    },
    {
      title: "Diagnostic Testing",
      description: "Advanced diagnostic services including blood work, X-rays, and laboratory testing.",
      price: "Starting at KSh 1,500",
      image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?auto=format&fit=crop&w=400&h=300",
      icon: Activity
    },
    {
      title: "Emergency Care",
      description: "24/7 emergency veterinary services for urgent medical situations and critical care.",
      price: "Call for pricing",
      image: "https://images.unsplash.com/photo-1472396961693-142e6e269027?auto=format&fit=crop&w=400&h=300",
      icon: Siren
    },
    {
      title: "Preventative Care",
      description: "Vaccination programs, wellness plans, and preventive treatments to keep your pet healthy.",
      price: "Starting at KSh 2,000",
      image: "https://images.unsplash.com/photo-**********-03cce0bbc87b?auto=format&fit=crop&w=400&h=300",
      icon: Shield
    },
    {
      title: "Parasite Control",
      description: "Comprehensive parasite prevention and treatment including deworming and flea control.",
      price: "Starting at KSh 1,200",
      image: "https://images.unsplash.com/photo-1452378174528-3090a4bba7b2?auto=format&fit=crop&w=400&h=300",
      icon: Bug
    },
    {
      title: "Local and International Travel",
      description: "Travel health certificates, vaccinations, and documentation for domestic and international travel.",
      price: "Starting at KSh 3,000",
      image: "https://images.unsplash.com/photo-1583337130417-3346a1be7dee?auto=format&fit=crop&w=400&h=300",
      icon: Plane
    },
    {
      title: "Wash & Grooming",
      description: "Professional pet grooming services including bathing, nail trimming, and coat care.",
      price: "Starting at KSh 1,800",
      image: "https://images.unsplash.com/photo-**********-8cc77767d783?auto=format&fit=crop&w=400&h=300",
      icon: Sparkles
    }
  ];

  const features = [
    {
      icon: Home,
      title: "Home Visits",
      description: "No stressful car rides or waiting rooms. We come to you.",
      gradient: "from-blue-100 to-cyan-100"
    },
    {
      icon: Calendar,
      title: "Flexible Scheduling",
      description: "Book appointments that fit your busy schedule.",
      gradient: "from-green-100 to-emerald-100"
    },
    {
      icon: Phone,
      title: "24/7 Support",
      description: "Emergency care available around the clock.",
      gradient: "from-purple-100 to-indigo-100"
    },
    {
      icon: Clock,
      title: "Quick Response",
      description: "Fast response times for urgent situations.",
      gradient: "from-orange-100 to-amber-100"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <Header />

      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 via-purple-600/5 to-green-600/5"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8 transform transition-all duration-1000 ease-out animate-fade-in">
              <div className="inline-flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-full px-6 py-3 shadow-lg border border-white/50">
                <Heart className="w-5 h-5 text-rose-500" />
                <span className="text-sm font-medium text-slate-700">Compassionate Mobile Care</span>
              </div>
              
              <h1 className="text-5xl md:text-7xl font-bold leading-tight">
                <span className="bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 bg-clip-text text-transparent">
                  Mobile Veterinary Care
                </span>
                <br />
                <span className="bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
                  At Your Doorstep
                </span>
              </h1>
              
              <p className="text-xl text-slate-600 leading-relaxed max-w-lg">
                Professional, compassionate veterinary services delivered to your home. 
                Reduce stress for your pets while ensuring they receive the best care possible.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-6">
                <Button
                  onClick={smoothScrollToAppointmentForm}
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
                >
                  <span>Book Appointment</span>
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
                <Button 
                  asChild 
                  variant="outline" 
                  size="lg"
                  className="border-2 border-slate-200 bg-white/70 backdrop-blur-sm text-slate-700 hover:bg-slate-50 hover:border-slate-300 px-8 py-4 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
                >
                  <Link to="/services">View Services</Link>
                </Button>
              </div>
              
              <div className="flex items-center space-x-6 pt-4">
                <div className="flex items-center space-x-2 text-slate-600">
                  <Phone className="w-5 h-5 text-blue-600" />
                  <span className="font-medium">Emergency: +254 718 376 311</span>
                </div>
              </div>
            </div>
            
            <div className="relative">
              <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl p-8 transform rotate-2 hover:rotate-0 transition-all duration-700 ease-out hover:shadow-3xl">
                <div className="absolute inset-0 bg-gradient-to-br from-white/50 to-blue-50/50 rounded-3xl"></div>
                <div className="relative text-center space-y-6">
                  <div className="relative mx-auto">
                    <div className="w-32 h-32 bg-gradient-to-br from-blue-100 to-blue-200 rounded-3xl flex items-center justify-center mx-auto shadow-lg">
                      <img 
                        src="https://images.unsplash.com/photo-1582562124811-c09040d0a901?auto=format&fit=crop&w=200&h=200" 
                        alt="Veterinarian"
                        className="w-24 h-24 rounded-2xl object-cover shadow-lg"
                      />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center shadow-lg">
                      <Heart className="w-4 h-4 text-white" />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <h3 className="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">Dr. Cynthia</h3>
                    <p className="text-blue-600 font-medium">Licensed Mobile Veterinarian</p>
                  </div>
                  
                  <p className="text-slate-600 leading-relaxed">
                    "10+ years of experience bringing quality veterinary care directly to your home with love and compassion."
                  </p>
                  
                  <div className="flex justify-center space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/30 via-white/30 to-green-50/30"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20 space-y-6">
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-100 to-green-100 rounded-2xl px-8 py-4">
              <Shield className="w-6 h-6 text-blue-600" />
              <span className="text-lg font-semibold text-slate-700">Why Choose Us</span>
            </div>
            
            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 bg-clip-text text-transparent leading-tight">
              Why Choose Mobile
              <br />
              <span className="bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
                Veterinary Care?
              </span>
            </h2>
            
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
              Experience the convenience and comfort of professional veterinary services 
              delivered directly to your home with unprecedented care and attention.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card 
                key={index} 
                className="group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl"
              >
                <CardContent className="p-8 text-center space-y-6">
                  <div className={`w-20 h-20 bg-gradient-to-r ${feature.gradient} rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                    <feature.icon className="w-10 h-10 text-slate-700" />
                  </div>
                  
                  <h3 className="text-xl font-bold text-slate-800 group-hover:text-blue-600 transition-colors duration-300">
                    {feature.title}
                  </h3>
                  
                  <p className="text-slate-600 leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-100/50 via-blue-50/50 to-green-50/50"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20 space-y-6">
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-green-100 to-emerald-100 rounded-2xl px-8 py-4">
              <Heart className="w-6 h-6 text-green-600" />
              <span className="text-lg font-semibold text-slate-700">Our Services</span>
            </div>
            
            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-900 via-green-900 to-slate-900 bg-clip-text text-transparent leading-tight">
              Comprehensive Care
              <br />
              <span className="bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                Tailored to Your Pet
              </span>
            </h2>
            
            <p className="text-xl text-slate-600 leading-relaxed">
              Professional veterinary services designed with your pet's comfort and health in mind
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {services.map((service, index) => (
              <Card 
                key={index} 
                className="group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl"
              >
                <div className="relative h-48 overflow-hidden rounded-t-3xl">
                  <img 
                    src={service.image} 
                    alt={service.title}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  <div className="absolute top-4 right-4 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg">
                    <service.icon className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
                
                <CardContent className="p-8 space-y-4">
                  <h3 className="text-xl font-bold text-slate-800 group-hover:text-blue-600 transition-colors duration-300">
                    {service.title}
                  </h3>
                  
                  <p className="text-slate-600 leading-relaxed">
                    {service.description}
                  </p>
                  
                  <p className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
                    {service.price}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-16">
            <Button 
              asChild 
              size="lg" 
              className="bg-gradient-to-r from-green-600 to-emerald-700 hover:from-green-700 hover:to-emerald-800 text-white px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
            >
              <Link to="/services" className="flex items-center space-x-2">
                <span>View All Services</span>
                <ArrowRight className="w-5 h-5" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-700"></div>
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto space-y-8">
            <h2 className="text-4xl md:text-5xl font-bold text-white leading-tight">
              Ready to Schedule Your Pet's 
              <br />
              <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                Appointment?
              </span>
            </h2>
            
            <p className="text-xl text-blue-100 leading-relaxed">
              Book now for convenient, stress-free veterinary care that puts your pet's comfort first
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button
                onClick={smoothScrollToAppointmentForm}
                size="lg"
                className="bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                <span>Book Online</span>
                <Calendar className="w-5 h-5 ml-2" />
              </Button>
              <Button 
                asChild 
                size="lg" 
                variant="outline" 
                className="text-white border-white/50 bg-white/10 backdrop-blur-sm hover:bg-white hover:text-blue-600 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                <a href="tel:+254718376311" className="flex items-center space-x-2">
                  <Phone className="w-5 h-5" />
                  <span>Call +254 718 376 311</span>
                </a>
              </Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Index;
