
import { useState } from 'react';
import { Link } from 'react-router-dom';
import { Phone, Mail, Clock, MessageCircle } from 'lucide-react';
import AdminLogin from './AdminLogin';

const Footer = () => {
  const [clickCount, setClickCount] = useState(0);
  const [showLogin, setShowLogin] = useState(false);

  const handleLogoClick = () => {
    setClickCount(prev => {
      const newCount = prev + 1;
      if (newCount === 4) {
        setShowLogin(true);
        return 0; // Reset counter
      }
      return newCount;
    });
  };

  return (
    <>
      <footer className="bg-slate-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <div 
                  className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center cursor-pointer transition-transform hover:scale-105"
                  onClick={handleLogoClick}
                >
                  <span className="text-white font-bold text-lg">C</span>
                </div>
                <div>
                  <h3 className="text-xl font-bold">CVETS Veterinary Services</h3>
                  <p className="text-sm text-slate-300">Professional Pet Care</p>
                </div>
              </div>
              <p className="text-slate-300 mb-4 max-w-md">
                Providing compassionate, professional veterinary care for your beloved pets. 
                Your pet's health and wellbeing are our top priorities.
              </p>
              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-slate-300">
                  <Phone className="w-4 h-4" />
                  <span>+254 718 376 311</span>
                </div>
                <div className="flex items-center space-x-2 text-slate-300">
                  <MessageCircle className="w-4 h-4" />
                  <span>WhatsApp: +254 718 376 311</span>
                </div>
                <div className="flex items-center space-x-2 text-slate-300">
                  <Mail className="w-4 h-4" />
                  <span><EMAIL></span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li><Link to="/services" className="text-slate-300 hover:text-white transition-colors">Our Services</Link></li>
                <li><Link to="/about" className="text-slate-300 hover:text-white transition-colors">About Us</Link></li>
                <li><Link to="/blog" className="text-slate-300 hover:text-white transition-colors">Pet Care Tips</Link></li>
                <li><Link to="/contact" className="text-slate-300 hover:text-white transition-colors">Book Appointment</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Service Hours</h4>
              <div className="space-y-2 text-slate-300">
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4" />
                  <span>Open 7 Days a Week</span>
                </div>
                <p>Monday - Sunday: 8AM - 6PM</p>
                <p className="text-red-300 font-medium">Emergency Services Available</p>
                <a 
                  href="https://wa.me/254718376311" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center space-x-2 text-green-400 hover:text-green-300 transition-colors mt-2"
                >
                  <MessageCircle className="w-4 h-4" />
                  <span>WhatsApp Us</span>
                </a>
              </div>
            </div>
          </div>

          <div className="border-t border-slate-700 mt-8 pt-8 text-center text-slate-400">
            <p>&copy; 2024 CVETS Veterinary Services. All rights reserved.</p>
          </div>
        </div>
      </footer>

      <AdminLogin open={showLogin} onOpenChange={setShowLogin} />
    </>
  );
};

export default Footer;
