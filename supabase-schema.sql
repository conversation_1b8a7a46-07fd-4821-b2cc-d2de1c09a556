-- CVETS Veterinary Services Database Schema
-- Run this SQL in your Supabase SQL editor to create the required tables

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Appointments table
CREATE TABLE IF NOT EXISTS appointments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50) NOT NULL,
    pet_name VA<PERSON>HAR(255) NOT NULL,
    pet_type VARCHAR(100) NOT NULL,
    service VARCHAR(255) NOT NULL,
    preferred_date DATE NOT NULL,
    preferred_time TIME NOT NULL,
    message TEXT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'cancelled', 'completed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    image_url TEXT,
    category VARCHAR(100) NOT NULL,
    in_stock BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Blog posts table
CREATE TABLE IF NOT EXISTS blog_posts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    image_url TEXT,
    author VARCHAR(255) NOT NULL,
    published BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);
CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments(preferred_date);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_products_in_stock ON products(in_stock);
CREATE INDEX IF NOT EXISTS idx_blog_posts_published ON blog_posts(published);
CREATE INDEX IF NOT EXISTS idx_blog_posts_created_at ON blog_posts(created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (adjust as needed for your security requirements)

-- Appointments: Allow insert for new appointments, select for admin
CREATE POLICY "Allow public to insert appointments" ON appointments
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public to view appointments" ON appointments
    FOR SELECT USING (true);

-- Products: Allow public to view, admin to manage
CREATE POLICY "Allow public to view products" ON products
    FOR SELECT USING (in_stock = true);

CREATE POLICY "Allow all operations on products" ON products
    FOR ALL USING (true);

-- Blog posts: Allow public to view published posts, admin to manage
CREATE POLICY "Allow public to view published blog posts" ON blog_posts
    FOR SELECT USING (published = true);

CREATE POLICY "Allow all operations on blog posts" ON blog_posts
    FOR ALL USING (true);

-- Insert some sample data

-- Sample services/categories for products
INSERT INTO products (name, description, price, category, image_url) VALUES
('Premium Dog Food', 'High-quality nutrition for adult dogs', 45.99, 'Food & Nutrition', 'https://images.unsplash.com/photo-1589924691995-400dc9ecc119?auto=format&fit=crop&w=400&h=300'),
('Cat Flea Treatment', 'Effective flea prevention and treatment', 29.99, 'Health & Medicine', 'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?auto=format&fit=crop&w=400&h=300'),
('Pet Grooming Kit', 'Complete grooming set for home use', 89.99, 'Grooming', 'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?auto=format&fit=crop&w=400&h=300'),
('Travel Carrier', 'Safe and comfortable pet travel carrier', 79.99, 'Travel & Transport', 'https://images.unsplash.com/photo-1583337130417-3346a1be7dee?auto=format&fit=crop&w=400&h=300');

-- Sample blog posts
INSERT INTO blog_posts (title, content, excerpt, author, published, image_url) VALUES
('10 Essential Pet Care Tips', 'Taking care of your pet involves more than just feeding them...', 'Learn the fundamental aspects of pet care that every owner should know.', 'Dr. Cynthia', true, 'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?auto=format&fit=crop&w=800&h=400'),
('Understanding Pet Vaccinations', 'Vaccinations are crucial for your pet''s health and longevity...', 'A comprehensive guide to pet vaccination schedules and importance.', 'Dr. Cynthia', true, 'https://images.unsplash.com/photo-1576201836106-db1758fd1c97?auto=format&fit=crop&w=800&h=400'),
('Preparing Your Pet for Travel', 'Whether it''s a local trip or international travel...', 'Essential tips for safe and stress-free pet travel.', 'Dr. Cynthia', true, 'https://images.unsplash.com/photo-1583337130417-3346a1be7dee?auto=format&fit=crop&w=800&h=400');
